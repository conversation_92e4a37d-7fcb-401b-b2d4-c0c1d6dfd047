import 'package:get/get.dart';
import 'package:attp_2024/features/location/presentation/controller/LocationController.dart';

import '../../../core/data/api/configs/dio_configs.dart';
import '../../../core/data/api/services/auth/auth_service.dart';
import '../../../core/data/prefs/prefs.dart';

class LocationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => LocationController());
    Get.lazyPut(() => Prefs(), fenix: true);
    Get.putAsync(() async => DioService().init());
    Get.lazyPut(() => AuthService());
  }
}
