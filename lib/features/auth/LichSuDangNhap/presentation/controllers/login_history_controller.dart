import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/api/services/auth/auth_service.dart';
import 'package:attp_2024/core/data/database/device_data.dart';
import 'package:attp_2024/core/data/dto/request/login_request_model.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/device_response.dart';
import 'package:attp_2024/core/data/dto/response/device_response_model.dart';
import 'package:attp_2024/core/data/dto/response/permission_response.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/fingerprint_use_case.dart';
import 'package:attp_2024/core/services/permission_use_case.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/utils/info_device.dart';

class LoginHistoryController extends GetxController {
  final AuthService _authService = AuthService();

  var isLoading = false.obs;

  var email = "";

  var isCheck = false.obs;

  var password = "".obs;

  UserAccessModel? userAccessModel;

  final TextEditingController passwordController = TextEditingController();

  var deviceSerice = DeviceData();

  DeviceResponse? infoDevice;

  List<DeviceResponseModel> listDevice = [];

  DeviceData deviceData = DeviceData();

  RxBool allowLoginWithFingerprint = false.obs;

  RxBool isFingerAuth = false.obs;

  var passwordError = "".obs;
  RxString platformType = "Unknown".obs;

  String getPlatformType() {
    if (Platform.isAndroid) {
      return "Android";
    } else if (Platform.isIOS) {
      return "iOS";
    } else {
      return "Unknown";
    }
  }

  @override
  void onInit() async {
    super.onInit();
    infoDevice = await InfoDevice.getDeviceData();
    platformType.value = getPlatformType();

    if (Get.arguments != null) {
      if (Get.arguments is UserAccessModel) {
        userAccessModel = Get.arguments;
        allowLoginWithFingerprint.value =
            await FingerprintUseCase.getSateFingerprintAuth();
        if (allowLoginWithFingerprint.value) {
          var response = await deviceData.getListDevice(
              request: ProcRequestModel(fields: [
            FieldModel(
                name: "UserID",
                type: "guid",
                value: userAccessModel?.userID ?? "")
          ]));
          if (response.status == Status.success) {
            listDevice = response.data ?? [];
            if (listDevice.isNotEmpty) {
              var deviceActive = listDevice.firstWhere(
                  (device) => device.thietBiCode == infoDevice?.thietBiCode);
              if (deviceActive != null) {
                isFingerAuth.value = !deviceActive.ngungSD;
              }
            }
          }
        }
      }
    }
  }

  Future<void> initDevice({required String userId}) async {
    deviceSerice.addDevice(
        request: ProcRequestModel(fields: [
      FieldModel(
          name: "MaThietBi",
          type: "String",
          value: infoDevice?.thietBiCode ?? ""),
      FieldModel(
          name: "TenThietBi",
          type: "String",
          value: infoDevice?.tenThietBi ?? ""),
      FieldModel(
          name: "HangSX", type: "String", value: infoDevice?.hangSX ?? ""),
      FieldModel(
          name: "Platform", type: "String", value: infoDevice?.platform ?? ""),
      FieldModel(
          name: "Version", type: "String", value: infoDevice?.version ?? ""),
      FieldModel(
          name: "LoaiThietBi",
          type: "String",
          value: infoDevice?.loaiThietBi ?? ""),
      FieldModel(name: "UserID", type: "guid", value: userId),
      FieldModel(name: "TrangThai", type: "bool", value: false),
    ]));
  }

  Future<void> login({String? passwordAuto}) async {
    isLoading.value = true;
    if (passwordAuto == null) {
      if (password.value.trim().isEmpty) {
        passwordError.value = "Không được bỏ trống";
        isLoading.value = false;
        return;
      }
    }
    try {
      String token = "";
      var response = await _authService.login(
        loginRequestModel: LoginRequestModel(
          userName: userAccessModel?.tenDangNhap.trim() ?? "",
          password: passwordAuto?.trim() ?? password.value.trim(),
          idDatabase: userAccessModel?.databaseId ?? "",
        ),
      );

      if (response.status == Status.success) {
        final responsePermission =
            await _authService.permission(response.data?.userId ?? "");
        if (userAccessModel != null) {
          UserUseCase.setUser(
              userAccessModel:
                  userAccessModel ?? UserAccessModel.defaultAccount);
        }
        if (responsePermission.status == Status.success) {
          List<PermissionResponseModel> data = responsePermission.data ?? [];
          await PermissionUseCase.setPermissions(listPermission: data);
        }
        token = response.data?.token ?? "";
      }

      if (token.isNotEmpty) {
        var deviceData = DeviceData();
        var result = await deviceData.getListDevice(
            request: ProcRequestModel(fields: [
          FieldModel(
              name: "UserID", type: "guid", value: response.data?.userId ?? "")
        ]));

        if (result.status == Status.success) {
          List<DeviceResponseModel> data = result.data ?? [];
          bool allowAdd = data.isEmpty ||
              !data.any((device) {
                return device.thietBiCode == infoDevice?.thietBiCode &&
                    device.userID == response.data?.userId;
              });

          if (allowAdd) {
            await initDevice(userId: response.data?.userId ?? "");
          }
        }

        if (isCheck.value == true) {
          UserUseCase.setKeepLogin();
        }
        Get.offAllNamed(Routes.main);
      } else {
        Get.snackbar("Đăng nhập thất bại", "Vui lòng kiểm tra lại thông tin đăng nhập!");
      }
    } catch (e) {
      Get.snackbar("Error", "Error logging in: $e");
      print("Error logging in: $e");
    } finally {
      isLoading.value = false;
    }
  }
}
