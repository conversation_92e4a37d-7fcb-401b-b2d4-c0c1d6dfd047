import 'package:flutter/cupertino.dart';
import 'package:gap/gap.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/controllers/login_controller.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class CustomButton extends StatelessWidget {
  final LoginController controller;
  const CustomButton({super.key, required this.controller});
  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: 7.h,
        width: double.infinity,
        child: CupertinoButton(
          color: AppColors.primary, // Màu nền của nút
          borderRadius: BorderRadius.circular(8), // Bo góc của nút
          padding: const EdgeInsets.symmetric(vertical: 12),
          onPressed: () {
            controller.login(context); // Hành động khi nhấn nút
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Đăng nhập',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w700,
                  color: CupertinoColors.white,
                  // Màu chữ
                ),
              ),
              const Gap(10),
              Icon(
                CupertinoIcons.arrow_right,
                color: CupertinoColors.white,
                size: 19.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
