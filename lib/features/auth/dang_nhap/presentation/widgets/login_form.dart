import 'package:attp_2024/core/ui/widgets/CustomYearPicker/CustomYearPicker.dart';
import 'package:flutter/cupertino.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_passwordField.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textfield.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/controllers/login_controller.dart';

class LoginForm extends GetView<LoginController> {
  const LoginForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Gap(3.h),
        Obx(() {
          return TextFieldWidget(
            title: 'Tên đăng nhập',
            hideTitle: true,
            errorWidget: controller.userNameError.value,
            prefixIcon: Icon(
              CupertinoIcons.person_circle_fill,
              size: 20.sp,
              color: AppColors.primary,
            ),
            placeholder: 'Tài khoản',
            initialValue: '',
            onChange: (item) {
              controller.userName = item;
              if (controller.userNameError.value != "") {
                controller.userNameError.value = "";
              }
            },
          );
        }),
        Obx(() {
          return PasswordFieldWidget(
            title: 'Mật khẩu',
            hideTitle: true,
            placeholder: 'Mật khẩu',
            initialValue: '',
            errorWidget: controller.passwordError.value,
            clearIcon: AppColors.primary,
            prefixIcon: Icon(
              CupertinoIcons.lock_circle_fill,
              size: 20.sp,
              color: AppColors.primary,
            ),
            onChange: (item) {
              controller.password = item;
              if (controller.passwordError.value != "") {
                controller.passwordError.value = "";
              }
            },
          );
        }),
        Obx(() {
          return CustomCombobox(
            hideTitle: true,
            defaultSelectedItem: controller.databaseSelected.value != null
                ? DropdownModel(
                    display: controller.databaseSelected.value?.tenTinh ??
                        "-Chọn tỉnh-",
                    id: controller.databaseSelected.value?.id ?? "")
                : null,
            // ignore: invalid_use_of_protected_member
            dropDownList: controller.listDataBase.value
                .map((items) =>
                    DropdownModel(display: items.tenSite, id: items.id))
                .toList(),
            onChange: (DropdownModel? selected) {
              controller.databaseSelected.value = controller.listDataBase
                  .firstWhere((item) => item.id == selected?.id);
            },
            title: 'Tỉnh',
          );
        }),
        const YearPickerWidget(title: 'Chọn năm:'),
      ],
    );
  }
}
