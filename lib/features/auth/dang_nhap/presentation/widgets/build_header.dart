// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:flutter/material.dart';
// import 'package:responsive_sizer/responsive_sizer.dart';

// class BuildHeader extends StatelessWidget {
//   const BuildHeader({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             Text(
//               'PHẦN MỀM',
//               style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                     fontWeight: FontWeight.w900,
//                     color: const Color(0xFF6C6A6A),
//                     fontSize: 21.sp,
//                   ),
//             ),
//             Text(
//               'AN TOÀN THỰC PHẨM',
//               style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                     fontWeight: FontWeight.w900,
//                     // color: Colors.blueGrey,
//                     color: const Color(0xFF6C6A6A),
//                     fontSize: 21.sp,
//                   ),
//             ),
//             Text(
//               'NTSOFT ATTP',
//               style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                     fontWeight: FontWeight.w900,
//                     // color: Colors.blueGrey,
//                     color: AppColors.primary,
//                     fontSize: 22.sp,
//                   ),
//             ),
//             //
//           ],
//         ),
//       ],
//     );
//   }
// }

import 'package:flutter/material.dart';
class BuildHeader extends StatelessWidget {
  const BuildHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset('assets/images/Logo2.png',
                width: 33, height: 33, fit: BoxFit.fill),
            const SizedBox(width: 15),
            Text(
              'Đăng nhập ',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontSize: 27,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 7),
        const Text(
          'Vui lòng đăng nhập để trải nghiệm ứng dụng',
          style: TextStyle(fontSize: 15, color: Colors.black54),
        ),
      ],
    );
  }
}
