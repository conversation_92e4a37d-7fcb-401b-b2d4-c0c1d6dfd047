import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/controllers/login_controller.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/widgets/build_footer.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/widgets/build_header.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/widgets/custom_checkbox_widget.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/widgets/login_form.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class LoginPage extends GetView<LoginController> {
  const LoginPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Loading.LoadingFullScreen(
      isLoading: controller.isLoading,
      body: _BuildBody(context),
    );
  }

  // ignore: non_constant_identifier_names
  Container _BuildBody(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppImageString.logoAuth),
          fit: BoxFit.cover,
        ),
      ),
      child: Center(
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Padding(
            padding: EdgeInsets.all(8.w),
            child: Column(
              children: [
                const BuildHeader(),
                SizedBox(height: 4.h),
                const LoginForm(),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 1.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomCheckButton(
                        value: controller.isCheck,
                        onChanged: (newValue) {
                          controller.isCheck.value = newValue ?? false;
                        },
                        activeColor: AppColors.activeColors,
                        checkColor: AppColors.checkColors,
                        fillColor: Colors.grey[300]!,
                      ),
                      InkWell(
                        onTap: () {
                          Get.back();
                          Get.toNamed(Routes.usedAccount);
                        },
                        child: TextWidget(
                          text: "Tài khoản đã đăng nhập",
                          color: AppColors.primary,
                          size: AppDimens.textSize12,
                        ),
                      )
                    ],
                  ),
                ),
                _buildButton(context),
                SizedBox(
                  height: 3.h,
                ),
                const BuildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Center _buildButton(BuildContext context) {
    return Center(
      child: SizedBox(
        height: 48,
        width: double.infinity,
        child: ElevatedButton(
          style:
              ElevatedButton.styleFrom(backgroundColor: AppColors.buttonColors),
          onPressed: () {
            controller.login(context);
          },
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Đăng nhập',
                style:
                    TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
              ),
              SizedBox(width: 10),
              Icon(
                Icons.arrow_forward,
                color: Colors.white,
              )
            ],
          ),
        ),
      ),
    );
  }
}
