import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/configs/dio_configs.dart';
import 'package:attp_2024/core/data/api/services/auth/auth_service.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/controllers/login_controller.dart';

class LoginBinding extends Bindings {
  @override
  void dependencies() {
    Get.putAsync<DioService>(() async {
      final dioService = DioService();
      return await dioService.init();
    });

    // Register other services
    Get.lazyPut(() => Prefs(), fenix: true);
    Get.lazyPut(() => AuthService());
    Get.lazyPut(() => LoginController());
    Get.lazyPut(() => DioService());
  }
}
