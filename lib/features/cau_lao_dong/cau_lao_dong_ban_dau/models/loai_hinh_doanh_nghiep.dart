class LoaiHinhDoanhNghiep {
  final String id;
  final String tenLoaiHinhDN;

  LoaiHinhDoanhNghiep({required this.id, required this.tenLoaiHinhDN});

  // <PERSON><PERSON><PERSON><PERSON> từ JSON sang Object
  factory LoaiHinhDoanhNghiep.fromJson(Map<String, dynamic> json) {
    return LoaiHinhDoanhNghiep(
      id: json['LoaiHinhDNID'] ?? '',
      tenLoaiHinhDN: json['TenLoaiHinhDN'] ?? '',
    );
  }

  // <PERSON>yển từ Object sang JSON
  Map<String, dynamic> toJson() {
    return {
      'LoaiHinhDNID': id,
      'TenLoaiHinhDN': tenLoaiHinhDN,
    };
  }
}
