class QuyMoLaoDong {
  final String id;
  final String tenQuyMoLaoDong;

  QuyMoLaoDong({required this.id, required this.tenQuyMoLaoDong});

  // Chuyển từ JSON sang Object
  factory QuyMoLaoDong.fromJson(Map<String, dynamic> json) {
    return QuyMoLaoDong(
      id: json['value'] ?? '',
      tenQuyMoLaoDong: json['display'] ?? '',
    );
  }

  // Chuyển từ Object sang JSON
  Map<String, dynamic> toJson() {
    return {
      'value': id,
      'display': tenQuyMoLaoDong,
    };
  }
}
