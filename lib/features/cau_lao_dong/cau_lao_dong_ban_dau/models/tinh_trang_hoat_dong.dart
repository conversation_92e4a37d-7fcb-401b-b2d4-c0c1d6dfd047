class TinhTrangHoatDong {
  final String id;
  final String tenTinhTrang;

  TinhTrangHoatDong({required this.id, required this.tenTinhTrang});

  // <PERSON>y<PERSON><PERSON> từ JSON sang Object
  factory TinhTrangHoatDong.fromJson(Map<String, dynamic> json) {
    return TinhTrangHoatDong(
      id: json['TinhTrangTGHDKTID'],
      tenTinhTrang: json['TenTinhTrangTGHDKT'],
    );
  }

  // Chuyển từ Object sang JSON
  Map<String, dynamic> toJson() {
    return {
      'TinhTrangTGHDKTID': id,
      'TenTinhTrangTGHDKT': tenTinhTrang,
    };
  }
}
