// import 'package:flutter/widgets.dart';

// Widget buildInput(
//       String thoiDiem, bool onlyRead, String id, String stt, String chiTieu) {
//     if (num.tryParse(thoiDiem) == null) {
//       return _buildCell(thoiDiem, onlyRead, false);
//     } else {
//       TextEditingController inputController =
//           TextEditingController(text: thoiDiem);
//       FocusNode focusNode = FocusNode();
//       String oldSoNguoi = '0';
//       focusNode.addListener(() {
//         if (focusNode.hasFocus) {
//           oldSoNguoi = inputController.text;
//         } else {
//           log(inputController.text);
//           controller.onUnFocusInputForm2(
//               id, stt, chiTieu, inputController, oldSoNguoi);
//         }
//       });

//       return Container(
//         padding: EdgeInsets.only(left: 6.w, right: 3.w),
//         height: 4.h,
//         width: 20.w,
//         alignment: Alignment.center,
//         child: TextField(
//           enabled: !onlyRead,
//           controller: inputController,
//           focusNode: focusNode,
//           textAlignVertical: TextAlignVertical.center,
//           keyboardType: TextInputType.number,
//           decoration: InputDecoration(
//             filled: onlyRead,
//             fillColor: AppColors.grey2,
//             contentPadding: EdgeInsets.symmetric(horizontal: 2.w),
//             enabledBorder: const OutlineInputBorder(
//               borderSide: BorderSide(width: 1, color: AppColors.grey),
//             ),
//             disabledBorder: const OutlineInputBorder(
//               borderSide: BorderSide(width: 1, color: AppColors.grey),
//             ),
//             border: const OutlineInputBorder(
//               borderSide: BorderSide(width: 1, color: AppColors.grey),
//             ),
//           ),
//         ),
//       );
//     }
//   }