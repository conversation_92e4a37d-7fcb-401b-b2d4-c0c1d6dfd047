import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

// ignore: must_be_immutable
class CustomMultiSelectCombobox extends StatelessWidget {
  var selectedItems = <DropdownModel>[].obs;
  var list = <DropdownModel>[].obs;
  final Function(List<DropdownModel> selected) onChange;
  final List<DropdownModel> dropDownList;
  final bool isEnabled;
  final bool isRequired;
  final String? errorText;
  final double? vertical;
  final double? horizontal;
  final double? borderRadius;
  final String title;
  final double? height;
  final String hintSearch;
  final String? titleSearch;
  final double? heighCombo;
  final bool showCloseButton;
  final List<DropdownModel>? defaultSelectedItems;

  CustomMultiSelectCombobox({
    super.key,
    required this.dropDownList,
    required this.onChange,
    this.isEnabled = true,
    this.isRequired = false,
    this.vertical,
    this.horizontal,
    this.borderRadius,
    this.height,
    required this.title,
    this.hintSearch = "Tìm kiếm ...",
    this.titleSearch,
    this.defaultSelectedItems,
    this.heighCombo,
    this.showCloseButton = true,
    this.errorText,
  }) {
    if (defaultSelectedItems != null) {
      selectedItems.assignAll(defaultSelectedItems!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: AppDimens.sizeTitle,
                  color: AppColors.black),
            ),
            if (isRequired)
              const Text(
                ' *',
                style: TextStyle(color: Colors.red, fontSize: 16),
              ),
          ],
        ),
        const SizedBox(height: 4),
        InkWell(
          onTap: isEnabled ? () => _showDropdown(context) : null,
          child: Container(
            height:
                selectedItems.isEmpty ? 45 : 45 + (selectedItems.length * 45),
            padding: EdgeInsets.symmetric(
              horizontal: horizontal ?? 10,
              vertical: vertical ?? 12,
            ),
            decoration: BoxDecoration(
              color: isEnabled ? Colors.white : Colors.grey[200],
              border: Border.all(color: AppColors.borderInput1, width: .5),
              borderRadius: BorderRadius.circular(borderRadius ?? 5),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  children: selectedItems.map((item) {
                    return Chip(
                      backgroundColor: const Color(0xffDFF5EB),
                      side: const BorderSide(),
                      label: Text(
                        item.display ?? "",
                        style: const TextStyle(fontWeight: FontWeight.normal),
                      ),
                      onDeleted: () {
                        selectedItems.removeWhere(
                            (selectedItem) => selectedItem == item);
                        onChange(selectedItems);
                      },
                    );
                  }).toList(),
                ),
                if (selectedItems.isEmpty)
                  Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 15, vertical: 0),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: const Color(0xffDFF5EB)),
                      child: const Text('-Chọn-')),
              ],
            ),
          ),
        ),
        Text(
          errorText ?? "",
          style: TextStyle(color: Colors.red, fontSize: AppDimens.smallText),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  void _showDropdown(BuildContext context) {
    list.assignAll(dropDownList);
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: const BeveledRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(5), topRight: Radius.circular(5))),
      builder: (BuildContext context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: SingleChildScrollView(
            child: Container(
              height: heighCombo ?? 90.h,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  if (titleSearch != null)
                    Padding(
                        padding: EdgeInsets.only(
                          top: 1.h,
                          bottom: 2.5.h,
                        ),
                        child: Center(
                          child: TextWidget(
                            text: titleSearch ?? "",
                            size: 18.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        )),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 1.w),
                    child: TextField(
                      onChanged: (value) {
                        searchOrganizations(value);
                      },
                      decoration: InputDecoration(
                        hintText: hintSearch,
                        hintStyle: const TextStyle(color: AppColors.gray2),
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[200],
                      ),
                    ),
                  ),
                  const SizedBox(height: 8.0),
                  Expanded(
                    child: Obx(() {
                      if (list.isEmpty) {
                        return Center(
                            child: Image.asset(
                                width: 30.w, AppImageString.iDataNotFound));
                      }
                      return ListView.builder(
                        itemCount: list.length,
                        itemBuilder: (context, index) {
                          final item = list[index];
                          return Obx(() {
                            final isSelected = selectedItems.contains(item);
                            return CheckboxListTile(
                              fillColor:
                                  WidgetStateProperty.all(AppColors.white),
                              checkColor: AppColors.primary,
                              title: Text(item.display!),
                              value: isSelected,
                              onChanged: (bool? value) {
                                if (value == true) {
                                  selectedItems.add(item);
                                } else {
                                  selectedItems.removeWhere(
                                      (selectedItem) => selectedItem == item);
                                }
                                onChange(selectedItems);
                                // errorText = '';
                              },
                            );
                          });
                        },
                      );
                    }),
                  ),
                  showCloseButton
                      ? InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                                color: AppColors.red2.withOpacity(.2),
                                borderRadius: BorderRadius.circular(5)),
                            margin: EdgeInsets.all(2.w),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  size: 20,
                                  Icons.close,
                                  color: AppColors.red2,
                                ),
                                SizedBox(
                                  width: 5,
                                ),
                                TextWidget(
                                  text: "Đóng",
                                  color: AppColors.red2,
                                  size: AppDimens.textSize16,
                                )
                              ],
                            ),
                          ),
                        )
                      : const SizedBox()
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void searchOrganizations(String query) {
    if (query.isEmpty) {
      list.assignAll(dropDownList);
    } else {
      list.assignAll(
        dropDownList
            .where((org) =>
                org.display != null &&
                org.display!.toLowerCase().contains(query.toLowerCase()))
            .toList(),
      );
    }
  }

  // bool validate() {
  //   if (isRequired && selectedItems.isEmpty) {
  //     errorText.value = 'This field is required';
  //     return false;
  //   }
  //   errorText.value = '';
  //   return true;
  // }
}
