import 'dart:developer';

import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/CustomDatePicker/custom_date_picker.dart';
import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/checkbox/check_box_widget.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textarea.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textfield.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_ban_dau/presentation/controller/cau_lao_dong_ban_dau_controller.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_ban_dau/widgets/multi_combobox.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class BuildForm1 extends GetView<CauLaoDongBanDauController> {
  const BuildForm1({super.key});
  @override
  Widget build(BuildContext context) {
    //------------------------------- Form ---------------------------------//
    final Rx<DropdownModel?> selectedItem = Rx<DropdownModel?>(null);

    //------------------------------- Form ---------------------------------//
    return SingleChildScrollView(
      padding: EdgeInsets.all(3.w),
      scrollDirection: Axis.vertical,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(
            () => CustomDatePicker(
              isRequired: true,
              title: 'Ngày thu thập',
              placeholder: 'DD/MM/YYYY',
              date: controller.ngayThuThap.value,
              setTime: controller.ngayThuThap.value,
              isMaximumDate: true,
              onChange: (DateTime? date) {
                controller.ngayThuThap.value = date!;
              },
              showClearButton: false,
            ),
          ),
          Row(
            spacing: 2.w,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Expanded(
                child: Obx(
                  () => CustomCombobox(
                    isRequired: true,
                    title: "Tổ chức",
                    dropDownList: controller.allToChuc
                        .map((item) => DropdownModel(
                            id: item.toChucID, display: item.tenToChuc))
                        .toList(),
                    onChange: (selected) {
                      if (selected != null) {
                        controller.onChangeToChuc(selected);
                      }
                    },
                    delete: () {},
                    defaultSelectedItem: controller.selectedToChuc.value,
                    showDelete: true,
                    errorText: controller.errorToChuc.value,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {},
                child: Container(
                  margin: EdgeInsets.only(bottom: 2.h),
                  decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(5)),
                  width: 45,
                  height: 45,
                  child: const Icon(
                    CupertinoIcons.add,
                    size: 30,
                    color: AppColors.white,
                  ),
                ),
              )
            ],
          ),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              // showClearButton: false,
              // isDisabled: true,
              title: 'Tên KCN/KTT',
              placeholder: 'Nhập tên KCN/KTT',
              initialValue: controller.tenKCN.value,
              setValue: controller.tenKCN.value,
              onChange: (value) {
                controller.tenKCN.value = value;
              },
              errorWidget: controller.errorKCN.value,
            ),
          ),
          Obx(
            () => CheckBoxWidget(
              title: 'Nằm trong KCN/KKT',
              borderColor: AppColors.borderColors,
              checkedBorderColor: AppColors.primary,
              fillColor: AppColors.white,
              checkedFillColor: AppColors.primary,
              checkColor: AppColors.white,
              value: controller.namTrongKCN.value,
              // isDisabled: true,
              onChanged: (value) {
                controller.namTrongKCN.value = value!;
                // form1.control('namTrongKCN').value = value;
              },
            ),
          ),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              // showClearButton: false,
              // isDisabled: true,
              title: 'Số ĐKKD/Mã số thuế',
              placeholder: '',
              initialValue: controller.maSoThue.value,
              setValue: controller.maSoThue.value,
              onChange: (value) {
                controller.maSoThue.value = value;
              },
              errorWidget: controller.errorMaSoThue.value,
            ),
          ),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              title: 'Tên người sử dụng lao động',
              // showClearButton: false,
              // isDisabled: true,
              placeholder: '',
              initialValue: controller.tenNguoiSDLD.value,
              setValue: controller.tenNguoiSDLD.value,
              onChange: (value) {
                controller.tenNguoiSDLD.value = value;
              },
              errorWidget: controller.errorTenNguoiSDLD.value,
            ),
          ),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              title: 'Số CCCD/CMND',
              // showClearButton: false,
              // isDisabled: true,
              placeholder: '',
              initialValue: controller.soCCCD.value,
              setValue: controller.soCCCD.value,
              onChange: (value) {
                controller.soCCCD.value = value;
              },
              errorWidget: controller.errorSoCCCD.value,
            ),
          ),
          Obx(
            () => CustomCombobox(
              isRequired: true,
              title: "Loại hình doanh nghiệp",
              dropDownList: controller.allLoaiHinhDoanhNghiep
                  .map((item) =>
                      DropdownModel(id: item.id, display: item.tenLoaiHinhDN))
                  .toList(),
              onChange: (DropdownModel? selected) {
                controller.selectedLoaiHinhDoanhNghiep.value = selected;
              },
              defaultSelectedItem: controller.selectedLoaiHinhDoanhNghiep.value,
              showDelete: true,
              errorText: controller.errorLoaiHinhDN.value,
            ),
          ),
          Obx(
            () => CustomCombobox(
              isRequired: true,
              title: "Tình trạng hoạt động",
              dropDownList: controller.allTinhTrangHoatDong
                  .map((item) =>
                      DropdownModel(id: item.id, display: item.tenTinhTrang))
                  .toList(),
              onChange: (DropdownModel? selected) {
                controller.selectedTinhTrangHoatDong.value = selected;
                log(controller.selectedTinhTrangHoatDong.value!.id.toString());
                log(controller.selectedTinhTrangHoatDong.value!.display
                    .toString());
              },
              defaultSelectedItem: controller.selectedTinhTrangHoatDong.value,
              showDelete: true,
              errorText: controller.errorTinhTrangHD.value,
            ),
          ),
          Obx(
            () => CustomDatePicker(
              isRequired: true,
              title: 'Ngày hoạt động',
              placeholder: 'DD/MM/YYYY',
              date: controller.ngayHoatDong.value,
              setTime: controller.ngayHoatDong.value,
              onChange: (DateTime? date) {
                log('$date', name: 'Ngày thu thập');
                controller.ngayHoatDong.value = date!;
                // field.didChange(date);
              },
              showClearButton: false,
            ),
          ),
          Obx(() {
            return CustomMultiSelectCombobox(
              title: 'Ngành nghề kinh doanh',
              isRequired: true,
              dropDownList: controller.allNganhNgheKinhDoanh
                  .map((e) => DropdownModel(id: e.value, display: e.display))
                  .toList(),
              onChange: (selectedIndustries) {
                if (selectedIndustries.isNotEmpty) {
                  controller.selectedNganhNghe.value = selectedIndustries;
                }
              },
              // ignore: invalid_use_of_protected_member
              defaultSelectedItems: controller.selectedNganhNghe.value,
              errorText: controller.errorNganhNghe.value,
            );
          }),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              title: 'Số điện thoại',
              placeholder: '',
              initialValue: controller.soDienThoai.value,
              setValue: controller.soDienThoai.value,
              onChange: (value) {
                controller.soDienThoai.value = value;
              },
              errorWidget: controller.errorSoDienThoai.value,
            ),
          ),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              title: 'Email',
              placeholder: '',
              initialValue: controller.email.value,
              setValue: controller.email.value,
              onChange: (value) {
                controller.email.value = value;
              },
              errorWidget: controller.errorEmail.value,
            ),
          ),
          Obx(() => CustomCombobox(
                isRequired: true,
                title: "Tỉnh/Thành phố",
                defaultSelectedItem: controller.selectedTinh.value,
                dropDownList: controller.allTinh
                    .map((item) => DropdownModel(
                          display: item.display,
                          id: item.value,
                        ))
                    .toList(),
                onChange: (DropdownModel? selected) {
                  log("tinhID:: ${selected?.id}");
                  controller.selectedTinh.value = selected;
                  controller.fetchHuyenByTinh(selected?.id ?? '');
                  controller.selectedHuyen.value = null;
                  controller.selectedXa.value = null;
                  controller.selectedThon.value = null;
                },
                delete: () {
                  controller.selectedTinh.value = null;
                  controller.selectedHuyen.value = null;
                  controller.selectedXa.value = null;
                  controller.selectedThon.value = null;
                },
                errorText: controller.errorTinh.value,
              )),
          // Chọn huyện
          Obx(() => CustomCombobox(
                isRequired: true,
                title: "Quận/Huyện",
                dropDownList: controller.allHuyen
                    .map((item) => DropdownModel(
                          display: item.display,
                          id: item.value,
                        ))
                    .toList(),
                defaultSelectedItem: controller.selectedHuyen.value,
                onChange: (DropdownModel? selected) {
                  controller.selectedHuyen.value = selected;
                  controller.fetchXaByHuyen(selected?.id ?? '');
                  controller.selectedXa.value = null;
                  controller.selectedThon.value = null;
                },
                delete: () {
                  controller.selectedHuyen.value = null;
                  controller.selectedXa.value = null;
                  controller.selectedThon.value = null;
                },
                errorText: controller.errorHuyen.value,
              )),
          // Chọn xã
          Obx(() => CustomCombobox(
                isRequired: true,
                title: "Phường/Xã",
                dropDownList: controller.allXa
                    .map((item) => DropdownModel(
                          display: item.display,
                          id: item.value,
                        ))
                    .toList(),
                defaultSelectedItem: controller.selectedXa.value,
                onChange: (DropdownModel? selected) {
                  controller.selectedXa.value = selected;
                  controller.fetchThonByXa(selected?.id ?? '');
                  controller.selectedThon.value = null;
                },
                delete: () {
                  controller.selectedXa.value = null;
                  controller.selectedThon.value = null;
                },
                errorText: controller.errorXa.value,
              )),
          // Chọn thôn
          Obx(() => CustomCombobox(
                title: "Thôn/Xóm",
                dropDownList: controller.allThon
                    .map((item) => DropdownModel(
                          display: item.display,
                          id: item.value,
                        ))
                    .toList(),
                defaultSelectedItem: controller.selectedThon.value,
                onChange: (DropdownModel? selected) {
                  controller.selectedThon.value = selected;
                },
                delete: () {
                  controller.selectedThon.value = null;
                },
              )),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              title: 'Số nhà',
              placeholder: '',
              initialValue: controller.soNha.value,
              setValue: controller.soNha.value,
              onChange: (value) {
                controller.soNha.value = value;
              },
              errorWidget: controller.errorSoNha.value,
            ),
          ),
          Obx(
            () => CustomCombobox(
              isRequired: true,
              title: "Quy mô lao động",
              dropDownList: controller.allQuyMoDoanhNghiep
                  .map((item) =>
                      DropdownModel(id: item.id, display: item.tenQuyMoLaoDong))
                  .toList(),
              onChange: (DropdownModel? selected) {
                controller.selectedQuyMoLaoDong.value = selected;
              },
              defaultSelectedItem: controller.selectedQuyMoLaoDong.value,
              showDelete: true,
              errorText: controller.errorQuyMoSDLD.value,
            ),
          ),
          Obx(
            () => TextFieldWidget(
              isRequired: true,
              title: 'Mặt hàng sản xuất dịch vụ chính',
              placeholder: '',
              initialValue: controller.matHangChinh.value,
              setValue: controller.matHangChinh.value,
              onChange: (value) {
                controller.matHangChinh.value = value;
              },
              errorWidget: controller.errorMatHangChinh.value,
            ),
          ),
          Obx(
            () => TextAreaWidget(
              title: 'Nội dung thu thập',
              initialValue: controller.noiDung.value,
              placeholder: 'Nội dung thu thập',
              onChange: (value) {
                controller.noiDung.value = value;
              },
              errorWidget: controller.errorNoiDung.value,
            ),
          ),
          const SizedBox(height: 20),
          ButtonWidget(
            ontap: () {
              if (controller.cauLaoDongID.value == '') {
                if (controller.validateForm()) {
                  controller.handleSubmit();
                }
              } else {
                controller.nextForm();
              }
            },
            text: "Tiếp tục",
            backgroundColor: AppColors.primary,
            borderRadius: 7,
          ),
        ],
      ),
    );
  }
}
