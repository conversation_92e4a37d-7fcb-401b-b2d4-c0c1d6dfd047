import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

Widget buildProcessBar(
    {required int currentStep,
    required List<String> titleStep,
    required int inProcess}) {
  return LayoutBuilder(builder: (context, constraints) {
    const double circleDiameter = 25;
    double horizontalPadding = 5.w;
    int nodeCount = titleStep.length;
    int connectorCount = nodeCount - 1;
    double fixedWidth = (nodeCount * circleDiameter) + (2 * horizontalPadding);
    double availableWidth = constraints.maxWidth - fixedWidth;
    double connectorWidth =
        availableWidth > 0 ? availableWidth / connectorCount : 0;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Stack(
        children: [
          Row(
            children: [
              SizedBox(width: horizontalPadding),
              ...List.generate(nodeCount, (index) {
                TextAlign align;
                if (index == 0) {
                  align = TextAlign.left;
                } else if (index == nodeCount - 1) {
                  align = TextAlign.right;
                } else {
                  align = TextAlign.center;
                }
                bool isActive = index < currentStep;
                return _buildNodeDynamic(
                  align,
                  titleStep[index],
                  isActive,
                  connectorWidth,
                  inProcess == index,
                );
              }),
              SizedBox(width: horizontalPadding),
            ],
          ),
        ],
      ),
    );
  });
}

Widget _buildNodeDynamic(TextAlign align, String title, bool success,
    double connectorWidth, bool inProcess) {
  return Column(
    crossAxisAlignment: align == TextAlign.left
        ? CrossAxisAlignment.start
        : align == TextAlign.right
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.center,
    children: [
      Row(
        children: [
          if (align == TextAlign.left)
            const SizedBox(width: 30)
          else
            Container(
              height: 3,
              width: connectorWidth,
              color: success ? AppColors.primary : AppColors.borderColors,
            ),
          Container(
            // HÌNH TRÒN
            width: 25,
            height: 25,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                  width: 2,
                  color: inProcess
                      ? AppColors.primary
                      : success
                          ? AppColors.primary
                          : AppColors.borderColors),
              color: inProcess
                  ? AppColors.white
                  : success
                      ? AppColors.primary
                      : AppColors.borderColors,
            ),
            child: success
                ? const Icon(
                    Icons.check,
                    color: AppColors.white,
                    size: 20,
                  )
                : const SizedBox.shrink(),
          ),
          if (align == TextAlign.right)
            const SizedBox(width: 15)
          else
            Container(
              height: 3,
              width: connectorWidth,
              color: success ? AppColors.primary : AppColors.borderColors,
            ),
        ],
      ),
      TextWidget(
        text: title,
        size: AppDimens.textSize14,
        color: AppColors.black,
      ),
    ],
  );
}



// Hướng dẫn sử dụng
// buildProcessBar(
//                   inProcess: controller.progressBarInProcess.value,
//                   currentStep: controller.progressBarCurrentStep.value,
//                   titleStep: [
//                     'Thông tin chung',
//                     'Thông tin lao động tổ chức',
//                     'Nhu cầu tuyển dụng',
//                     'Văn bản',
//                   ]),