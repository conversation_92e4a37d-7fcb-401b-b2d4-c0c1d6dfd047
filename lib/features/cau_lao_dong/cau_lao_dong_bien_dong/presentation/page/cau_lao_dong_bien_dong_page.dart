import 'dart:developer';

import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/presentation/controller/cau_lao_dong_bien_dong_controller.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/widgets/form1.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/widgets/form2.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/widgets/form3.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/widgets/form4.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/widgets/progress_bar_circle.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';

class CauLaoDongBienDongPage extends GetView<CauLaoDongBienDongController> {
  const CauLaoDongBienDongPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Loading.LoadingFullScreen(
      isLoading: controller.isLoading,
      body: Scaffold(
          appBar: const AppBarWidget(
            title: "Thu thập nhu cầu lao động biến động",
            titleSize: 18,
          ),
          body: _buildBody(context)),
    );
  }

  // ignore: non_constant_identifier_names
  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        Container(
          color: AppColors.white,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 3.w, horizontal: 3.w),
            child: Obx(() => StepProgressWidget(
                  currentStep: controller.progressBarCurrentStep.value,
                  totalStep: controller.progressBarTotalStep.value,
                  nameStep: controller.progressBarNameStep.value,
                )),
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Obx(
              () => controller.currentForm.value == 1
                  ? const BuildForm1()
                  : controller.currentForm.value == 2
                      ? const BuildForm2()
                      : controller.currentForm.value == 3
                          ? const BuildForm3()
                          : controller.currentForm.value == 4
                              ? const BuildForm4()
                              : Container(), // Tránh lỗi null
            ),
          ),
        ),
        Obx(
          () => controller.currentForm.value > 1
              ? Row(
                  children: [
                    Expanded(
                      child: ButtonWidget(
                        ontap: () {
                          controller.previousForm();
                        },
                        text: "Quay về",
                        backgroundColor: AppColors.borderInputDisabled,
                        borderRadius: 0.0,
                      ),
                    ),
                    Expanded(
                      child: ButtonWidget(
                        ontap: () {
                          controller.nextForm();
                        },
                        text: controller.currentForm.value < 4
                            ? "Tiếp tục"
                            : "Kết thúc",
                        backgroundColor: AppColors.primary,
                        borderRadius: 0.0,
                      ),
                    ),
                  ],
                )
              : const SizedBox.shrink(),
        ),
        // Obx(() => Text(controller.tinhList.toString())),
      ],
    );
  }
}
