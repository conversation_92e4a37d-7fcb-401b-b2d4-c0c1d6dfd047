import 'dart:developer';
import 'package:attp_2024/core/configs/contanst/field_constants.dart';
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/configs/contents/messages/app_errors.dart';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/data/api/services/uploads/upload_service.dart';
import 'package:attp_2024/core/data/database/category_until_data.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/category_abs_response.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/du_lieu_mau_nhu_cau_tuyen_dung.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/loai_hinh_doanh_nghiep.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/du_lieu_mau_nguoi_lao_dong.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/quy_mo_lao_dong.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/tinh_trang_hoat_dong.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/to_chuc.dart';
import 'package:flutter/widgets.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

class CauLaoDongBienDongController extends GetxController {
  CategoryUntilData categoryUntilData = CategoryUntilData();
  UserAccessModel? userAccessModel;
  final _procService = ProcService();
  // Manager
  RxInt currentForm = 1.obs;
  RxInt progressBarCurrentStep = 1.obs; //Giai đoạn
  RxInt progressBarTotalStep = 4.obs; //Node
  RxString progressBarNameStep = ''.obs; //Node

  String procGetDataToChuc = 'Proc_Mobile_GetAll_Combo_ToChuc';
  String procGetDataQuyMoLaoDong = 'Proc_Mobile_GetAllQuyMoLaoDong';
  String procGetDataLoaiHinhDN = 'Proc_Mobile_GetCombo_LoaiHinhDN';
  String procGetDataTinhTrangHoatDong = 'Proc_Mobile_GetCombo_TinhTrangTGHDKT';
  String procGetDataDuLieuMauNguoiLaoDong =
      'Proc_Mobile_LayDuLieuMauNguoiLaoDong';
  String procGetDataDuLieuMauNhuCauTuyenDung =
      'Proc_Mobile_LayDuLieuMauNhuCauTuyenDung';

  final List<Map<String, dynamic>> nullBody = [];

  RxBool isLoading = false.obs;

  RxList<ToChuc> allToChuc = <ToChuc>[].obs;
  RxList<CategoryAbsResponse> allTinh = <CategoryAbsResponse>[].obs;
  RxList<CategoryAbsResponse> allHuyen = <CategoryAbsResponse>[].obs;
  RxList<CategoryAbsResponse> allXa = <CategoryAbsResponse>[].obs;
  RxList<CategoryAbsResponse> allThon = <CategoryAbsResponse>[].obs;
  RxList<CategoryAbsResponse> allNganhNgheKinhDoanh =
      <CategoryAbsResponse>[].obs;

  Rx<DropdownModel?> selectedToChuc = Rx<DropdownModel?>(null);
  Rx<DropdownModel?> selectedTinh = Rx<DropdownModel?>(null);
  Rx<DropdownModel?> selectedHuyen = Rx<DropdownModel?>(null);
  Rx<DropdownModel?> selectedXa = Rx<DropdownModel?>(null);
  Rx<DropdownModel?> selectedThon = Rx<DropdownModel?>(null);
  Rx<DropdownModel?> selectedLoaiHinhDoanhNghiep = Rx<DropdownModel?>(null);
  Rx<DropdownModel?> selectedTinhTrangHoatDong = Rx<DropdownModel?>(null);
  Rx<DropdownModel?> selectedQuyMoLaoDong = Rx<DropdownModel?>(null);
  RxList<DropdownModel> selectedNganhNghe = <DropdownModel>[].obs;

  //Form 2
  RxList<DuLieuMauNguoiLaoDong> allDuLieuMauNguoiLaoDong =
      <DuLieuMauNguoiLaoDong>[].obs;
  RxList<DuLieuMauNhuCauTuyenDung> allDuLieuMauNhuCauTuyenDung =
      <DuLieuMauNhuCauTuyenDung>[].obs;

  var ngayThuThap = DateTime.now().obs;
  var tenKCN = ''.obs;
  var namTrongKCN = false.obs;
  var soDKKD = ''.obs;
  var maSoThue = ''.obs;
  var tenNguoiSDLD = ''.obs;
  var soCCCD = ''.obs;
  var email = ''.obs;
  var soDienThoai = ''.obs;
  var soNha = ''.obs;
  var diaChiTC = ''.obs;
  var matHangChinh = ''.obs;
  var noiDung = ''.obs;
  var ngayHoatDong = DateTime.now().obs;
  var namSD = DateTime.now().year.toString().obs;
  var donViID = ''.obs;
  var userID = ''.obs;
  var siteUrl = ''.obs;
  var token = ''.obs;
  var cauLaoDongID = ''.obs;
  var noiDungForm4 = ''.obs;
  var chuKyPath = ''.obs;
  var chuKyString = ''.obs;
  var nguoiKy = ''.obs;
  var latitude = ''.obs;
  var longitude = ''.obs;

  //---------------------------------------------------- Validate
  var errorToChuc = ''.obs;
  var errorKCN = ''.obs;
  var errorMaSoThue = ''.obs;
  var errorTenNguoiSDLD = ''.obs;
  var errorSoCCCD = ''.obs;
  var errorTinh = ''.obs;
  var errorHuyen = ''.obs;
  var errorXa = ''.obs;
  var errorLoaiHinhDN = ''.obs;
  var errorTinhTrangHD = ''.obs;
  var errorNganhNghe = ''.obs;
  var errorSoDienThoai = ''.obs;
  var errorEmail = ''.obs;
  var errorSoNha = ''.obs;
  var errorQuyMoSDLD = ''.obs;
  var errorMatHangChinh = ''.obs;
  var errorNoiDung = ''.obs;
  // Form 3
  var errorDinhKem = ''.obs;
  var errorChuKy = ''.obs;
  var errorNguoiKy = ''.obs;

  //----------------------------------------------------------------

  // var test_ID_Cau_Lao_Dong = '4A1FFDDD-BAF1-42FB-B93B-50614A33E0B7'.obs;

  RxList<TinhTrangHoatDong> allTinhTrangHoatDong = <TinhTrangHoatDong>[].obs;
  RxList<LoaiHinhDoanhNghiep> allLoaiHinhDoanhNghiep =
      <LoaiHinhDoanhNghiep>[].obs;
  RxList<QuyMoLaoDong> allQuyMoDoanhNghiep = <QuyMoLaoDong>[].obs;

  final coreService = CoreService();

//------------------------------------------------------ Xử lý file
  var selectedFiles = <String>[].obs;
  var selectedValueString = '';

  void addFiles(List<String> files) {
    selectedFiles.addAll(files);
  }

  void removeFile(String file) {
    selectedFiles.remove(file);
  }

  void clearFiles() {
    selectedFiles.clear();
  }

  void pickImageFromCamera() async {
    isLoading.value = true;
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);

    if (image != null) {
      try {
        print('Đường dẫn ảnh: ${image.path}');
        selectedFiles.add(image.path);
      } catch (e) {
        print('Error uploading image: $e');
      }
    } else {
      isLoading.value = false;
    }
    isLoading.value = false;
  }

//------------------------------------------------------ Xử lý file

  @override
  void onInit() async {
    super.onInit();
    progressBarNameStep.value = 'Thông tin chung';
    await fetchLocation();
    await fetchTogether();
    everAll([
      selectedToChuc,
      tenKCN,
      maSoThue,
      tenNguoiSDLD,
      soCCCD,
      selectedLoaiHinhDoanhNghiep,
      selectedTinhTrangHoatDong,
      selectedNganhNghe,
      soDienThoai,
      email,
      selectedTinh,
      selectedHuyen,
      selectedXa,
      soNha,
      selectedQuyMoLaoDong,
      matHangChinh,
      noiDung,
    ], (_) => validateForm());
    everAll([nguoiKy, chuKyPath], (_) => validateForm4());
  }

  Future<void> fetchLocation() async {
    // Thêm lấy tọa độ vào đầu tiên
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        SnackbarUtil.showError('Vui lòng cấp quyền truy cập vị trí');
        return;
      }
    }

    Position position = await Geolocator.getCurrentPosition(
      // ignore: deprecated_member_use
      desiredAccuracy: LocationAccuracy.high,
    );

    latitude.value = position.latitude.toString();
    longitude.value = position.longitude.toString();

    log('Vị trí hiện tại: ${position.latitude}, ${position.longitude}');
  }

  Future<void> fetchTogether() async {
    isLoading.value = true;
    try {
      // Các fetch data khác
      await fetchAllToChuc();
      await fetchAllTinh();
      await fetchAllTinhTrangHoatDong();
      await fetchAllLoaiHinhDoanhNghiep();
      await fetchAllQuyMoDoanhNghiep();
      await fetchAllNganhNgheKinhDoanh();
      await fetchAllDuLieuMauNguoiLaoDong(cauLaoDongID.value);
      await fetchAllDuLieuMauNhuCauTuyenDung(cauLaoDongID.value);
      await loadInfoProfile();
    } catch (e) {
      log('Lỗi khi lấy dữ liệu: $e');
      SnackbarUtil.showError('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      isLoading.value = false;
    }
  }

  bool validateForm() {
    bool isValid = true; // Biến để theo dõi tính hợp lệ của form
    // log('$isValid', name: 'debug');
    // Kiểm tra selectedToChuc
    if (selectedToChuc.value == null) {
      errorToChuc.value = 'Vui lòng chọn tổ chức';
      isValid = false;
    } else {
      errorToChuc.value = '';
    }

    if (tenKCN.value == '') {
      errorKCN.value = 'Vui lòng nhập khu công nghiệp';
      isValid = false;
    } else {
      errorKCN.value = '';
    }

    if (maSoThue.value == '') {
      errorMaSoThue.value = 'Vui lòng nhập số ĐKKD';
      isValid = false;
    } else {
      errorMaSoThue.value = '';
    }

    if (tenNguoiSDLD.value == '') {
      errorTenNguoiSDLD.value = 'Vui lòng nhập tên người sử dụng lao động';
      isValid = false;
    } else {
      errorTenNguoiSDLD.value = '';
    }

    if (soCCCD.value == '') {
      errorSoCCCD.value = 'Vui lòng nhập số CCCD/CMND';
      isValid = false;
    } else {
      errorSoCCCD.value = '';
    }

    // Kiểm tra selectedLoaiHinhDoanhNghiep
    if (selectedLoaiHinhDoanhNghiep.value == null) {
      errorLoaiHinhDN.value = 'Vui lòng chọn loại hình doanh nghiệp';
      isValid = false;
    } else {
      errorLoaiHinhDN.value = '';
    }
    // Kiểm tra selectedTinhTrangHoatDong
    if (selectedTinhTrangHoatDong.value == null) {
      errorTinhTrangHD.value = 'Vui lòng chọn tình trạng hoạt động';
      isValid = false;
    } else {
      errorTinhTrangHD.value = '';
    }
    // Kiểm tra selectedNganhNghe (giả sử phải có ít nhất một phần tử)
    if (selectedNganhNghe.isEmpty) {
      errorNganhNghe.value = 'Vui lòng chọn ngành nghề';
      isValid = false;
    } else {
      errorNganhNghe.value = '';
    }
    // Kiểm tra soDienThoai
    if (soDienThoai.value == '') {
      errorSoDienThoai.value = 'Vui lòng nhập số điện thoại';
      isValid = false;
    } else {
      errorSoDienThoai.value = '';
    }

    // Kiểm tra email (kiểm tra cả định dạng email)
    if (email.value == '') {
      errorEmail.value = 'Vui lòng nhập email';
      isValid = false;
    } else if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email.value)) {
      errorEmail.value = 'Email không hợp lệ';
      isValid = false;
    } else {
      errorEmail.value = '';
    }

    if (selectedTinh.value == null) {
      errorTinh.value = 'Vui lòng chọn Tỉnh/Thành phố';
      isValid = false;
    } else {
      errorTinh.value = '';
    }
    if (selectedHuyen.value == null) {
      errorHuyen.value = 'Vui lòng chọn Quận/Huyện';
      isValid = false;
    } else {
      errorHuyen.value = '';
    }
    if (selectedXa.value == null) {
      errorXa.value = 'Vui lòng chọn Phường/Xã';
      isValid = false;
    } else {
      errorXa.value = '';
    }
    // Kiểm tra soNha
    if (soNha.value == '') {
      errorSoNha.value = 'Vui lòng nhập số nhà';
      isValid = false;
    } else {
      errorSoNha.value = '';
    }
    // Kiểm tra selectedQuyMoLaoDong
    if (selectedQuyMoLaoDong.value == null) {
      errorQuyMoSDLD.value = 'Vui lòng chọn quy mô lao động';
      isValid = false;
    } else {
      errorQuyMoSDLD.value = '';
    }

    // Kiểm tra matHangChinh
    if (matHangChinh.value == '') {
      errorMatHangChinh.value = 'Vui lòng chọn mặt hàng chính';
      isValid = false;
    } else {
      errorMatHangChinh.value = '';
    }

    // Kiểm tra noiDung
    if (noiDung.value == '') {
      errorNoiDung.value = 'Vui lòng nhập nội dung';
      isValid = false;
    } else {
      errorNoiDung.value = '';
    }

    return isValid;
  }

  bool validateForm4() {
    bool isValid = true; // Biến để theo dõi tính hợp lệ của form
    // log('$isValid', name: 'debug');
    // Kiểm tra selectedToChuc
    if (nguoiKy.value == '') {
      errorNguoiKy.value = 'Vui lòng nhập người ký';
      isValid = false;
    } else {
      errorNguoiKy.value = '';
    }

    if (chuKyPath.value == '') {
      errorChuKy.value = 'Vui lòng cung cấp chữ ký';
      isValid = false;
    } else {
      errorChuKy.value = '';
    }

    return isValid;
  }

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    namSD.value = userAccessModel!.year.toString();
    donViID.value = userAccessModel!.donViID.toString();
    userID.value = userAccessModel!.userID.toString();
    siteUrl.value = userAccessModel!.siteURL.toString();
    token.value = userAccessModel!.token.toString();
    // log(
    //   'năm SD: ${namSD.value},  donViID: ${donViID.value}, userID: ${userID.value}, siteUrl: ${siteUrl.value}',
    //   name: 'UseAccessModel',
    // );
  }

  Future<void> fetchAllToChuc() async {
    var response = await coreService.callProcData<ToChuc>(
        proc: procGetDataToChuc,
        fromJson: ToChuc.fromJson,
        request: [
          {
            "name": "TinhID",
            "type": "Guid",
            "value": "00000000-0000-0000-0000-000000000000"
          },
          {
            "name": "HuyenID",
            "type": "Guid",
            "value": "00000000-0000-0000-0000-000000000000"
          },
          {
            "name": "XaID",
            "type": "Guid",
            "value": "00000000-0000-0000-0000-000000000000"
          },
          {
            "name": "ThonID",
            "type": "Guid",
            "value": "00000000-0000-0000-0000-000000000000"
          },
          {
            "name": "NamThaoTac",
            "type": "Int",
            "value": "2024"
          } ///////////////////////////////////////////ĐANG GÁN CỨNG
        ]);
    if (response.status == Status.success) {
      allToChuc.value = response.data ?? [];
    }
  }

  Future<void> fetchAllLoaiHinhDoanhNghiep() async {
    var response = await coreService.callProcData<LoaiHinhDoanhNghiep>(
      proc: procGetDataLoaiHinhDN,
      fromJson: LoaiHinhDoanhNghiep.fromJson,
    );
    if (response.status == Status.success) {
      allLoaiHinhDoanhNghiep.value = response.data ?? [];
    }
  }

  Future<void> fetchAllTinhTrangHoatDong() async {
    var response = await coreService.callProcData<TinhTrangHoatDong>(
      proc: ProcConstants.getAllTinhTrangHoatDong,
      fromJson: TinhTrangHoatDong.fromJson,
    );
    if (response.status == Status.success) {
      allTinhTrangHoatDong.value = response.data ?? [];
    }
  }

  Future<void> fetchAllQuyMoDoanhNghiep() async {
    var response = await coreService.callProcData<QuyMoLaoDong>(
      proc: procGetDataQuyMoLaoDong,
      fromJson: QuyMoLaoDong.fromJson,
    );
    if (response.status == Status.success) {
      allQuyMoDoanhNghiep.value = response.data ?? [];
    }
  }

  // Future<void> testFetchHuyen() async {
  //   isLoading.value = true;

  //   final requestModel = ProcRequestModel(fields: [
  //     FieldModel(
  //         name: FieldConstants.tinhID,
  //         type: "guid",
  //         value: 'eb7e57a6-6193-4bd2-a9be-0c15aa2d17e0')
  //   ]);
  //   var response = await coreService.callProcData<CategoryAbsResponse>(
  //       proc: ProcConstants.getAllTinhTrangHoatDong,
  //       fromJson: CategoryAbsResponse.fromJson,
  //       request: requestModel.toJson());
  //   if (response.status == Status.success) {
  //     allHuyen.value = response.data ?? [];
  //     log(allHuyen.toString());
  //   } else {
  //     log("Lỗi: ${response.error}");
  //   }
  //   isLoading.value = false;
  // }

  //========================= TỈNH - HUYỆN - XÃ - THÔN =========================
  Future<void> fetchAllTinh() async {
    var responseTinh = await categoryUntilData.fetchAllTinh();
    if (responseTinh.status == Status.success) {
      allTinh.value = responseTinh.data ?? [];
    }
  }

  Future<void> fetchHuyenByTinh(String tinhID) async {
    final requestModel = ProcRequestModel(fields: [
      FieldModel(name: FieldConstants.tinhID, type: "guid", value: tinhID)
    ]);
    var responseHuyen =
        await categoryUntilData.fetchAllHuyenByTinh(tinhRequest: requestModel);
    if (responseHuyen.status == Status.success) {
      allHuyen.value = responseHuyen.data ?? [];
    }
  }

  Future<void> fetchXaByHuyen(String huyenId) async {
    final requestModel = ProcRequestModel(fields: [
      FieldModel(name: FieldConstants.huyenID, type: "guid", value: huyenId)
    ]);
    var responseXa =
        await categoryUntilData.fetchAllXaByHuyen(huyenRequest: requestModel);
    if (responseXa.status == Status.success) {
      allXa.value = responseXa.data ?? [];
    }
  }

  Future<void> fetchThonByXa(String xaId) async {
    final requestModel = ProcRequestModel(fields: [
      FieldModel(name: FieldConstants.xaID, type: "guid", value: xaId)
    ]);
    var responseThon =
        await categoryUntilData.fetchAllThonByXa(xaRequest: requestModel);
    if (responseThon.status == Status.success) {
      allThon.value = responseThon.data ?? [];
    }
  }

  Future<void> fetchAllNganhNgheKinhDoanh() async {
    var response = await categoryUntilData.fetchAllNganhNgheKinhDoanh();
    if (response.status == Status.success) {
      allNganhNgheKinhDoanh.assignAll(response.data ?? []);
    }
  }

  Future<void> fetchAllDuLieuMauNguoiLaoDong(String cauLaoDongID) async {
    var response = await coreService.callProcData<DuLieuMauNguoiLaoDong>(
      proc: procGetDataDuLieuMauNguoiLaoDong,
      fromJson: DuLieuMauNguoiLaoDong.fromJson,
      request: [
        {"name": "CauLaoDongID", "type": "guid", "value": cauLaoDongID}
      ],
    );
    if (response.status == Status.success) {
      allDuLieuMauNguoiLaoDong.assignAll(response.data ?? []);
    }
  }

  Future<void> fetchAllDuLieuMauNhuCauTuyenDung(String cauLaoDongID) async {
    var response = await coreService.callProcData<DuLieuMauNhuCauTuyenDung>(
      proc: procGetDataDuLieuMauNhuCauTuyenDung,
      fromJson: DuLieuMauNhuCauTuyenDung.fromJson,
      request: [
        {"name": "CauLaoDongID", "type": "guid", "value": cauLaoDongID}
      ],
    );
    if (response.status == Status.success) {
      allDuLieuMauNhuCauTuyenDung.assignAll(response.data ?? []);
    }
  }

  //==================================Xử lý==========================================

  void nextForm() async {
    if (currentForm.value == 1) {
      progressBarNameStep.value = 'Thông tin lao động tổ chức';
      progressBarCurrentStep.value += 1;
      currentForm.value += 1;
    } else if (currentForm.value == 2) {
      progressBarNameStep.value = 'Nhu cầu tuyển dụng';
      progressBarCurrentStep.value += 1;
      currentForm.value += 1;
    } else if (currentForm.value == 3) {
      progressBarNameStep.value = 'Văn bản';
      progressBarCurrentStep.value += 1;
      currentForm.value += 1;
    } else if (currentForm.value == 4) {
      if (validateForm4()) {
        submit();
        // controller.nextForm();
      }
    } else {
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi chuyển trang, vui lòng thoát ra vào lại",
          alignment: "bottom");
      // Get.back();
    }
  }

  void previousForm() async {
    currentForm.value -= 1;
    progressBarCurrentStep.value -= 1;
    if (currentForm.value == 1) {
      progressBarNameStep.value = 'Thông tin chung';
    } else if (currentForm.value == 2) {
      progressBarNameStep.value = 'Thông tin lao động tổ chức';
    } else if (currentForm.value == 3) {
      progressBarNameStep.value = 'Nhu cầu tuyển dụng';
    }
  }

  Future<void> onChangeToChuc(DropdownModel toChuc) async {
    ToChuc toChucSelected =
        allToChuc.firstWhere((item) => item.toChucID == toChuc.id);

    await fetchAllTinh();
    await fetchHuyenByTinh(toChucSelected.diaBanHCIDTinh);
    await fetchXaByHuyen(toChucSelected.diaBanHCIDHuyen);
    await fetchThonByXa(toChucSelected.diaBanHCIDXa);

    selectedToChuc.value = DropdownModel(
      id: toChucSelected.toChucID,
      display: toChucSelected.tenToChuc,
    );
    tenKCN.value = toChucSelected.tenKCN;
    namTrongKCN.value = toChucSelected.namTrongKCN;
    soDKKD.value = toChucSelected.soDKKD;
    maSoThue.value = toChucSelected.maSoThue;
    tenNguoiSDLD.value = toChucSelected.tenNguoiSuDungLD;
    soCCCD.value = toChucSelected.soCCCD;
    email.value = toChucSelected.email;
    soDienThoai.value = toChucSelected.soDienThoai;
    soNha.value = toChucSelected.soNha;
    diaChiTC.value = toChucSelected.diaChiCuThe;
    // log(toChucSelected.ngayHoatDong.toString(), name: 'akr');
    ngayHoatDong.value = DateTime.parse(toChucSelected.ngayHoatDong.toString());

    selectedNganhNghe.assignAll(
      allNganhNgheKinhDoanh
          .where((e) =>
              toChucSelected.nganhKinhTeID.contains(e.value) &&
              e.value.isNotEmpty)
          .map((e) => DropdownModel(id: e.value, display: e.display))
          .toList(),
    );

    LoaiHinhDoanhNghiep? loaiHinhDoanhNghiep = allLoaiHinhDoanhNghiep
        .firstWhereOrNull((item) => item.id == toChucSelected.loaiHinhDNID);
    selectedLoaiHinhDoanhNghiep.value = loaiHinhDoanhNghiep != null
        ? DropdownModel(
            id: loaiHinhDoanhNghiep.id,
            display: loaiHinhDoanhNghiep.tenLoaiHinhDN)
        : null;

    TinhTrangHoatDong? tinhTrangHoatDong =
        allTinhTrangHoatDong.firstWhereOrNull(
            (item) => item.id == toChucSelected.tinhTrangTGHDKTID);
    selectedTinhTrangHoatDong.value = tinhTrangHoatDong != null
        ? DropdownModel(
            id: tinhTrangHoatDong.id,
            display: tinhTrangHoatDong.tenTinhTrang,
          )
        : null;

    log(toChucSelected.diaBanHCIDTinh, name: 'debug');
    CategoryAbsResponse? tinh = allTinh.firstWhereOrNull(
        (item) => item.value == toChucSelected.diaBanHCIDTinh);
    selectedTinh.value = tinh != null
        ? DropdownModel(
            id: tinh.value,
            display: tinh.display,
          )
        : null;

    CategoryAbsResponse? huyen = allHuyen.firstWhereOrNull(
        (item) => item.value == toChucSelected.diaBanHCIDHuyen);
    selectedHuyen.value = huyen != null
        ? DropdownModel(
            id: huyen.value,
            display: huyen.display,
          )
        : null;

    CategoryAbsResponse? xa = allXa
        .firstWhereOrNull((item) => item.value == toChucSelected.diaBanHCIDXa);
    selectedXa.value = xa != null
        ? DropdownModel(
            id: xa.value,
            display: xa.display,
          )
        : null;

    // log(toChucSelected.diaBanHCIDThon.toString(), name: 'akr');
    CategoryAbsResponse? thon = allThon.firstWhereOrNull(
        (item) => item.value == toChucSelected.diaBanHCIDThon);
    selectedThon.value = thon != null
        ? DropdownModel(
            id: thon.value,
            display: thon.display,
          )
        : null;

    // selectedTinh.value = DropdownModel(
    //   id: toChucSelected.diaBanHCIDTinh,
    // );
  }

  void logInfo() {
    // log('ngày tt: ${ngayThuThap.value}');
    // log('tc:: ${selectedToChuc.value!.id ?? "nul"}');
    // //Mặt hàng chính
    // log('qmld: ${selectedQuyMoLaoDong.value!.id ?? "nul"}');
    // log('lhdn: ${selectedLoaiHinhDoanhNghiep.value!.id ?? "nul"}');
    // //Ngành nghề
    // log('sN: ${soNha.value}');
    // // log('tinh+ ${selectedTinh.value!.id ?? "nul"}');
    // // log('huyen+ ${selectedHuyen.value!.id ?? "nul"}');
    // // log('xa+ ${selectedXa.value!.id ?? "nul"}');
    // // log('thon+ ${selectedThon.value!.id ?? "nul"}');
    // log(diaChi.value);
    // log(soDienThoai.value);
    // log(email.value);
    // //Tình trạng hd kt
    // log(noiDung.value);
    // log(namTrongKCN.value.toString());
  }

  Future<void> doDuLieuMauVaoCauLaoDong(
      String cauLaoDongID, String tenBang) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "CauLaoDongID", "type": "guid", "value": cauLaoDongID}
    ];
    String proc = tenBang == 'CauLaoDongTTLD'
        ? 'Proc_Mobile_DoDuLieuMau_Vao_CauLaoDongTTLD'
        : tenBang == 'CauLaoDongTD'
            ? 'Proc_Mobile_DoDuLieuMau_Vao_CauLaoDongTD'
            : '';
    try {
      log('Tiến hành đổ dữ liệu mẫu vào $tenBang');
      var response = await _procService.callProc(proc, body);
      if (response.isNotEmpty) {
        log('Đổ dữ liệu mẫu thành công');
        await fetchAllDuLieuMauNguoiLaoDong(cauLaoDongID);
        await fetchAllDuLieuMauNhuCauTuyenDung(cauLaoDongID);
      }
    } catch (e) {
      log("$e");
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi gửi dữ liệu, vui lòng thử vào lúc khác!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> doDuLieuMauVaoCauLaoDongTD(String cauLaoDongID) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "CauLaoDongID", "type": "guid", "value": cauLaoDongID}
    ];
    try {
      log('Tiến hành đổ dữ liệu mẫu');
      var response = await _procService.callProc(
          "Proc_Mobile_DoDuLieuMau_Vao_CauLaoDongTD", body);
      if (response.isNotEmpty) {
        log('Đổ dữ liệu mẫu thành công');
        await fetchAllDuLieuMauNguoiLaoDong(cauLaoDongID);
      }
    } catch (e) {
      log("$e");
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi gửi dữ liệu, vui lòng thử vào lúc khác!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> handleLuuSoNguoiCauLaoDongTTLDByID(
      String cauLaoDongTTLDID, String soNguoi) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "CauLaoDongTTLDID", "type": "guid", "value": cauLaoDongTTLDID},
      {"name": "SoNguoi", "type": "String", "value": soNguoi}
    ];
    try {
      log('Tiến hành đổ dữ liệu mẫu');
      var response = await _procService.callProc(
          "Proc_Mobile_LuuThongTinSoNguoiCauLaoDongTTLD", body);
      if (response.isNotEmpty) {
        log('Đỗ số người vô cột thành công');
        updateLocalDuLieuMauNguoiLaoDong(cauLaoDongTTLDID, soNguoi);
      }
    } catch (e) {
      log("$e");
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi gửi dữ liệu, vui lòng thử vào lúc khác!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> handleLuuSoNguoiCauLaoDongTDByID(
      String cauLaoDongTDID, String tenCot, String soNguoi) async {
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "CauLaoDongTDID", "type": "guid", "value": cauLaoDongTDID},
      {"name": "TenCot", "type": "String", "value": tenCot},
      {"name": "SoNguoi", "type": "String", "value": soNguoi}
    ];
    try {
      log('Tiến hành đổ dữ liệu mẫu');
      var response = await _procService.callProc(
          "Proc_Mobile_LuuThongTinSoNguoiCauLaoDongTD", body);
      if (response.isNotEmpty) {
        log('Đỗ số người vô cột thành công');
        updateLocalDuLieuMauNhuCauTuyenDung(
            cauLaoDongTDID, tenCot, double.parse(soNguoi));
      }
    } catch (e) {
      log("$e");
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi gửi dữ liệu, vui lòng thử vào lúc khác!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> handleSubmit() async {
    log('gửi dữ liệu ');
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "Loai", "type": "string", "value": "them"},
      {"name": "CauLaoDongID", "type": "guid", "value": ""},
      {
        "name": "NgayThuThap",
        "type": "DateTime",
        "value": DateFormat('yyyy-MM-dd').format(ngayThuThap.value)
      },
      {
        "name": "ToChucID",
        "type": "guid",
        "value": selectedToChuc.value!.id ?? ""
      },
      {"name": "MatHangChinh", "type": "string", "value": matHangChinh.value},
      {
        "name": "QuyMoSDLD_CLD",
        "type": "guid",
        "value": selectedQuyMoLaoDong.value!.id ?? ""
      },
      {
        "name": "LoaiHinhDNID_TC",
        "type": "guid",
        "value": selectedLoaiHinhDoanhNghiep.value!.id ?? ""
      },
      {
        "name": "NganhNgheID_TC",
        "type": "string",
        "value":
            selectedNganhNghe.map((e) => '"${e.id ?? ""}"').toList().toString(),
      },
      {"name": "SoNha_TC", "type": "string", "value": soNha.value},
      {
        "name": "ThonID_TC",
        "type": "guid",
        "value": selectedThon.value!.id ?? ""
      },
      {"name": "XaID_TC", "type": "guid", "value": selectedXa.value!.id ?? ""},
      {
        "name": "HuyenID_TC",
        "type": "guid",
        "value": selectedHuyen.value!.id ?? ""
      },
      {
        "name": "TinhID_TC",
        "type": "guid",
        "value": selectedTinh.value!.id ?? ""
      },
      {"name": "DiaChi_TC", "type": "string", "value": diaChiTC.value},
      {"name": "SoDienThoai", "type": "string", "value": soDienThoai.value},
      {"name": "Email", "type": "string", "value": email.value},
      {
        "name": "TinhTrangHDID",
        "type": "guid",
        "value": selectedTinhTrangHoatDong.value!.id ?? ""
      },
      {"name": "NoiDungTT_CLD", "type": "string", "value": noiDung.value},
      {"name": "NamTrongKCN", "type": "bool", "value": namTrongKCN.value},
      {"name": "KinhDo", "type": "string", "value": longitude.value},
      {"name": "ViDo", "type": "string", "value": latitude.value},
      {"name": "NamThuThap", "type": "number", "value": namSD.value},
      {"name": "DonViID", "type": "guid", "value": donViID.value},
      {"name": "UserID", "type": "guid", "value": userID.value},
      {"name": "DinhKem", "type": "string", "value": "DinhKem"},
      {"name": "ChuKy", "type": "string", "value": "ChuKyDinhKem"},
      {
        "name": "NguoiCungCapTT",
        "type": "string",
        "value": "chuKyModel.NguoiCungCapTT.text"
      }
    ];
    try {
      var response = await _procService.callProc(
          "Proc_Mobile_LuuThongTinCauLaoDongBienDong", body);
      if (response.isNotEmpty) {
        if (response.isNotEmpty &&
            response[0] is Map &&
            response[0]["CauLaoDongID"] != null) {
          cauLaoDongID.value = response[0]["CauLaoDongID"];
          await doDuLieuMauVaoCauLaoDong(cauLaoDongID.value, 'CauLaoDongTTLD');
          await doDuLieuMauVaoCauLaoDong(cauLaoDongID.value, 'CauLaoDongTD');
          nextForm();
          SnackbarUtil.showSuccess("Cập nhật thành công !",
              alignment: "bottom");
        } else {
          SnackbarUtil.showError("Có lỗi xảy ra khi gửi dữ liệu!",
              alignment: "bottom");
        }
      } else {
        SnackbarUtil.showError("Có lỗi xảy ra khi gửi dữ liệu!",
            alignment: "bottom");
      }
    } catch (e) {
      log("$e");
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi gửi dữ liệu, vui lòng thử vào lúc khác!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }

  int sumAll(List<String> mangKT, String thuThuHienTai, String giaTriHienTai) {
    DuLieuMauNguoiLaoDong nld;
    int sumAll = 0;
    for (String item in mangKT) {
      nld = allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == item);
      if (item != thuThuHienTai) {
        sumAll += int.parse(nld.soNguoi);
      }
    }
    sumAll += int.parse(giaTriHienTai);
    return sumAll;
  }

  void updateLocalDuLieuMauNguoiLaoDong(
      String cauLaoDongTTLDID, String soNguoi) {
    allDuLieuMauNguoiLaoDong
        .firstWhereOrNull((item) => item.cauLaoDongTTLDID == cauLaoDongTTLDID)
        ?.soNguoi = soNguoi;
    allDuLieuMauNguoiLaoDong.refresh();
  }

  void onUnFocusInputForm2(String id, String stt, String chiTieu,
      TextEditingController controller, String oldSoNguoi) async {
    log('id: $id');
    log('số tt: $stt');
    // log('số người: $soNguoi');
    log('chỉ tiêu: $chiTieu');
    String soNguoi = controller.text.trim();
    DuLieuMauNguoiLaoDong tongSoLD =
        allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == '4');

    if (stt == '4.1') {
      await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
      await handleLuuSoNguoiCauLaoDongTTLDByID(
          tongSoLD.cauLaoDongTTLDID, soNguoi);
      // Cập nhật tống số người local
    } else if (stt == '4.2' ||
        stt == '4.3' ||
        stt == '4.4' ||
        stt == '4.5' ||
        stt == '4.6' ||
        stt == '4.7') {
      if (int.parse(soNguoi) > int.parse(tongSoLD.soNguoi)) {
        SnackbarUtil.showWarning(
            '$chiTieu không được lớn hơn Tổng số lao động đang làm việc!',
            alignment: 'bottom');
        controller.text = oldSoNguoi;
      } else {
        await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
        if (stt == '4.5') {
          // con sẽ thành 0
          DuLieuMauNguoiLaoDong con = allDuLieuMauNguoiLaoDong
              .firstWhere((item) => item.cauLaoDongMauCode == '10');
          await handleLuuSoNguoiCauLaoDongTTLDByID(con.cauLaoDongTTLDID, '0');
        }
        if (stt == '4.6') {
          // con sẽ thành 0
          DuLieuMauNguoiLaoDong con = allDuLieuMauNguoiLaoDong
              .firstWhere((item) => item.cauLaoDongMauCode == '12');
          await handleLuuSoNguoiCauLaoDongTTLDByID(con.cauLaoDongTTLDID, '0');
        }
      }
    } else if (stt.isEmpty) {
      //XỬ LÝ LĐ NỮ TRONG 4.5 VS 4.6
      DuLieuMauNguoiLaoDong mauCodeLD = allDuLieuMauNguoiLaoDong
          .firstWhere((item) => item.cauLaoDongTTLDID == id);
      if (mauCodeLD.cauLaoDongMauCode == '10') {
        DuLieuMauNguoiLaoDong soLDKyHDLD =
            allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == '4.5');
        if (int.parse(soNguoi) > int.parse(soLDKyHDLD.soNguoi)) {
          SnackbarUtil.showWarning(
              'Lao động là nữ không được lớn hơn ${soLDKyHDLD.chiTieu}!',
              alignment: 'bottom');
          controller.text = oldSoNguoi;
        } else {
          await handleLuuSoNguoiCauLaoDongTTLDByID(
              mauCodeLD.cauLaoDongTTLDID, soNguoi);
        }
      } else if (mauCodeLD.cauLaoDongMauCode == '12') {
        DuLieuMauNguoiLaoDong soLDNuocNgoai =
            allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == '4.6');
        if (int.parse(soNguoi) > int.parse(soLDNuocNgoai.soNguoi)) {
          SnackbarUtil.showWarning(
              'Lao động là nữ không được lớn hơn ${soLDNuocNgoai.chiTieu}!',
              alignment: 'bottom');
          controller.text = oldSoNguoi;
        } else {
          await handleLuuSoNguoiCauLaoDongTTLDByID(
              mauCodeLD.cauLaoDongTTLDID, soNguoi);
        }
      } else {
        SnackbarUtil.showError(AppErrors.getFailed, alignment: "bottom");
        controller.text = oldSoNguoi;
      }
    } else if (stt == '5.1' ||
        stt == '5.2' ||
        stt == '5.3' ||
        stt == '5.4' ||
        stt == '5.5' ||
        stt == '5.6' ||
        stt == '5.7') {
      DuLieuMauNguoiLaoDong cotTong =
          allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == '5');
      var subRows5 = ['5.1', '5.2', '5.3', '5.4', '5.5', '5.6', '5.7'];
      DuLieuMauNguoiLaoDong t =
          allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == stt);
      int sum = sumAll(subRows5, t.thuTu, controller.text);
      if (sum > int.parse(tongSoLD.soNguoi)) {
        SnackbarUtil.showWarning(
            '${cotTong.chiTieu} không được lớn hơn Tổng số lao động đang làm việc!',
            alignment: 'bottom');
        controller.text = t.soNguoi;
      } else {
        await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
        await handleLuuSoNguoiCauLaoDongTTLDByID(
            cotTong.cauLaoDongTTLDID, sum.toString());
      }
    } else if (stt == '6.1' ||
        stt == '6.2' ||
        stt == '6.3' ||
        stt == '6.4' ||
        stt == '6.5' ||
        stt == '6.6' ||
        stt == '6.7' ||
        stt == '6.8') {
      // Kiểm tra Lao động đang làm việc chia theo nhóm nghề
      var subRows6 = [
        '6.1',
        '6.2',
        '6.3',
        '6.4',
        '6.5',
        '6.6',
        '6.7',
        '6.8'
      ]; // Danh sách các mã hàng con
      DuLieuMauNguoiLaoDong cotTong =
          allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == '6');
      DuLieuMauNguoiLaoDong t =
          allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == stt);
      int sum = sumAll(subRows6, t.thuTu, controller.text);
      if (sum > int.parse(tongSoLD.soNguoi)) {
        SnackbarUtil.showWarning(
            '${cotTong.chiTieu} không được lớn hơn Tổng số lao động đang làm việc!',
            alignment: 'bottom');
        controller.text = t.soNguoi;
      } else {
        await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
        await handleLuuSoNguoiCauLaoDongTTLDByID(
            cotTong.cauLaoDongTTLDID, sum.toString());
      }
    } else if (stt == '7.1' ||
        stt == '7.2' ||
        stt == '7.3' ||
        stt == '7.4' ||
        stt == '7.5' ||
        stt == '7.6') {
      var subRows7 = [
        '7.1',
        '7.2',
        '7.3',
        '7.4',
        '7.5',
        '7.6'
      ]; // Danh sách các mã hàng con
      DuLieuMauNguoiLaoDong cotTong =
          allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == '7');
      DuLieuMauNguoiLaoDong t =
          allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == stt);
      int sum = sumAll(subRows7, t.thuTu, controller.text);
      if (sum > int.parse(tongSoLD.soNguoi)) {
        SnackbarUtil.showWarning(
            '${cotTong.chiTieu} không được lớn hơn Tổng số lao động đang làm việc!',
            alignment: 'bottom');
        controller.text = t.soNguoi;
      } else {
        await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
        await handleLuuSoNguoiCauLaoDongTTLDByID(
            cotTong.cauLaoDongTTLDID, sum.toString());
      }
    } else if (stt == '10.1' || stt == '10.2' || stt == '10.3') {
      var subRows10 = ['10.1', '10.2', '10.3']; // Danh sách các mã hàng con
      DuLieuMauNguoiLaoDong cotTong =
          allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == '10');
      DuLieuMauNguoiLaoDong t =
          allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == stt);
      int sum = sumAll(subRows10, t.thuTu, controller.text);
      if (sum > int.parse(tongSoLD.soNguoi)) {
        SnackbarUtil.showWarning(
            '${cotTong.chiTieu} không được lớn hơn Tổng số lao động đang làm việc!',
            alignment: 'bottom');
        controller.text = t.soNguoi;
      } else {
        await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
        await handleLuuSoNguoiCauLaoDongTTLDByID(
            cotTong.cauLaoDongTTLDID, sum.toString());
      }
    } else if (stt == 'a' ||
        stt == 'b' ||
        stt == 'c' ||
        stt == 'đ' ||
        stt == 'e' ||
        stt == 'g' ||
        stt == 'h') {
      var subRows9 = ['a', 'b', 'c', 'đ', 'e', 'g', 'h'];
      DuLieuMauNguoiLaoDong cotTong =
          allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == '9');
      DuLieuMauNguoiLaoDong cotTong2 =
          allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == '9.1');
      DuLieuMauNguoiLaoDong cotTong3 =
          allDuLieuMauNguoiLaoDong.firstWhere((i) => i.thuTu == '9.2');
      DuLieuMauNguoiLaoDong t =
          allDuLieuMauNguoiLaoDong.firstWhere((item) => item.thuTu == stt);
      int sum = sumAll(subRows9, t.thuTu, controller.text);
      await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
      await handleLuuSoNguoiCauLaoDongTTLDByID(
          cotTong.cauLaoDongTTLDID, sum.toString());
      await handleLuuSoNguoiCauLaoDongTTLDByID(
          cotTong2.cauLaoDongTTLDID, sum.toString());
      await handleLuuSoNguoiCauLaoDongTTLDByID(
          cotTong3.cauLaoDongTTLDID, sum.toString());
    } else {
      await handleLuuSoNguoiCauLaoDongTTLDByID(id, soNguoi);
    }
  }

  void updateLocalDuLieuMauNhuCauTuyenDung(
      String cauLaoDongTDID, String tenCot, double soNguoi) {
    if (tenCot == 'SoLuongNu') {
      allDuLieuMauNhuCauTuyenDung
          .firstWhereOrNull((item) => item.cauLaoDongTDID == cauLaoDongTDID)
          ?.soLuongNu = soNguoi;
    } else {
      allDuLieuMauNhuCauTuyenDung
          .firstWhereOrNull((item) => item.cauLaoDongTDID == cauLaoDongTDID)
          ?.tongSo = soNguoi;
    }
    allDuLieuMauNhuCauTuyenDung.refresh();
  }

  void onUnFocusInputForm3(String tenCot, String id,
      TextEditingController controller, String oldValue) async {
    // log('id: ${id}');
    // log('giá trị vừa nhập: ${controller.text}');
    // log('giá trị cơ bản: $oldValue');
    // log('Tên cột: ${tenCot}');
    if (tenCot == 'SoLuongNu') {
      DuLieuMauNhuCauTuyenDung cotTong =
          allDuLieuMauNhuCauTuyenDung.firstWhere((i) => i.cauLaoDongTDID == id);
      if (int.parse(controller.text) >
          int.parse(cotTong.tongSo.toInt().toString())) {
        SnackbarUtil.showWarning(
            'Số lượng nữ không được lớn hơn tổng số lượng người !',
            alignment: 'bottom');
        controller.text = oldValue;
      } else {
        await handleLuuSoNguoiCauLaoDongTDByID(id, tenCot, controller.text);
      }
    } else {
      await handleLuuSoNguoiCauLaoDongTDByID(id, tenCot, controller.text);
    }
  }

  Future<void> uploadAttachFiles() async {
    try {
      final res = await UploadService().uploadMultiFiles(
          filePaths: selectedFiles, loaiVB: "HA", formName: "CauLDBienDong");
      if (res == null) {
        SnackbarUtil.showError("Tải tệp đính kèm thất bại !",
            alignment: "bottom");
        return;
      }

      if (res.data == null || res.data['data'] == null) {
        // log('Response data is null or invalid', name: 'UploadAttachFiles');
        SnackbarUtil.showError("Dữ liệu phản hồi không hợp lệ!",
            alignment: "bottom");
      }

      try {
        final dataList = res.data['data'] as List;
        selectedValueString = dataList.map((item) {
          try {
            return formatFileName(item.toString());
          } catch (e) {
            log('Error formatting filename: $e');
          }
        }).join('');

        log('uploadAttachFiles DONE: $selectedValueString');
      } catch (e) {
        log('Error processing response data: $e', name: 'UploadAttachFiles');
        SnackbarUtil.showError("Lỗi xử lý dữ liệu phản hồi!",
            alignment: "bottom");
      }
    } catch (e) {
      log("Lỗi khi lưu tệp tin: $e");
      SnackbarUtil.showError("Có lỗi xảy ra khi tải tệp tin!",
          alignment: "bottom");
    }
  }

  Future<void> uploadSignature() async {
    try {
      final res = await UploadService().uploadFile(
          filePath: chuKyPath.value, loaiVB: "HA", formName: "CauLDBienDong");
      if (res == null) {
        SnackbarUtil.showError("Tải tệp đính kèm thất bại !",
            alignment: "bottom");
        return;
      }

      if (res.data == null || res.data['data'] == null) {
        log('Response data is null or invalid', name: 'UploadAttachFiles');
        SnackbarUtil.showError("Dữ liệu phản hồi không hợp lệ!",
            alignment: "bottom");
      }

      try {
        final dataList = res.data['data'] as List;
        chuKyString.value = dataList.map((item) {
          try {
            return formatFileName(item.toString());
          } catch (e) {
            log('Error formatting filename: $e');
          }
        }).join('');

        log('uploadSignature DONE: $chuKyString');
      } catch (e) {
        log('Error processing response data: $e', name: 'UploadChuKy');
        SnackbarUtil.showError("Lỗi xử lý dữ liệu phản hồi!",
            alignment: "bottom");
      }
    } catch (e) {
      log("Lỗi khi lưu tệp tin: $e");
      SnackbarUtil.showError("Có lỗi xảy ra khi tải tệp tin!",
          alignment: "bottom");
    }
  }

  Future<void> handleUpdate() async {
    // log('Nội dung: ${noiDungForm4.value}');
    // log('Ng Ký: ${nguoiKy.value}');
    isLoading.value = true;
    final List<Map<String, dynamic>> body = [
      {"name": "CauLaoDongID", "type": "guid", "value": cauLaoDongID.value},
      // {
      //   "name": "CauLaoDongID",
      //   "type": "guid",
      //   "value": test_ID_Cau_Lao_Dong
      // },
      {"name": "DinhKem", "type": "string", "value": selectedValueString},
      {"name": "ChuKy", "type": "string", "value": chuKyString.value},
      {"name": "NguoiKy", "type": "string", "value": nguoiKy.value},
      {"name": "KinhDo", "type": "string", "value": longitude.value},
      {"name": "ViDo", "type": "string", "value": latitude.value},
      {"name": "NamThuThap", "type": "int", "value": namSD.value},
      {"name": "DonViID", "type": "guid", "value": donViID.value},
      {"name": "UserID", "type": "guid", "value": userID.value},
    ];
    try {
      var response = await _procService.callProc(
          "Proc_Mobile_UpdateCauLaoDongBanDau", body);
      if (response.isNotEmpty) {
        SnackbarUtil.showSuccess("Cập nhật thành công !", alignment: "bottom");
        Get.back();
      } else {
        SnackbarUtil.showError("Có lỗi xảy ra khi gửi dữ liệu!",
            alignment: "bottom");
      }
    } catch (e) {
      log("$e");
      SnackbarUtil.showError(
          "Có lỗi xảy ra khi gửi dữ liệu, vui lòng thử vào lúc khác!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> submit() async {
    try {
      isLoading.value = true;
      if (selectedFiles.isNotEmpty) {
        await uploadAttachFiles();
      }
      if (chuKyPath.value != '') {
        await uploadSignature();
      }
      await handleUpdate();
    } catch (e) {
      log("Lỗi khi submit: $e");
      SnackbarUtil.showError("Có lỗi xảy ra khi gửi dữ liệu!",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }
}
