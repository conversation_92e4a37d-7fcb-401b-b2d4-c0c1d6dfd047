class Tinh {
  final String tenDiaBan;
  final String maDiaBanHC;
  final String diaBanHCID;

  Tinh({
    required this.tenDiaBan,
    required this.maDiaBanHC,
    required this.diaBanHCID,
  });

  factory Tinh.fromJson(Map<String, dynamic> json) {
    return Tinh(
      tenDiaBan: json['TenDiaBan'],
      maDiaBanHC: json['MaDiaBanHC'],
      diaBanHCID: json['DiaBanHCID'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'TenDiaBan': tenDiaBan,
      'MaDiaBanHC': maDiaBanHC,
      'DiaBanHCID': diaBanHCID,
    };
  }
}
