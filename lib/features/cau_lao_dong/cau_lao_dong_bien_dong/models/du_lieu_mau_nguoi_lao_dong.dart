class DuLieuMauNguoiLaoDong {
  final String cauLaoDongTTLDID;
  final String cauLaoDongID;
  final String cauLaoDongMauID;
  final String cauLaoDongMauCode;
  final String thuTu;
  final String chiTieu;
  final String chiTieu1;
  String soNguoi;
  final bool inDam;
  final bool inNghien;
  final bool chiDoc;
  final String chuoiSQL;
  final String? congThuc;
  final String nganhNgheID;
  final double stt;

  DuLieuMauNguoiLaoDong({
    required this.cauLaoDongTTLDID,
    required this.cauLaoDongID,
    required this.cauLaoDongMauID,
    required this.cauLaoDongMauCode,
    required this.thuTu,
    required this.chiTieu,
    required this.chiTieu1,
    required this.soNguoi,
    required this.inDam,
    required this.inNghien,
    required this.chiDoc,
    required this.chuoiSQL,
    this.congThuc,
    required this.nganhNgheID,
    required this.stt,
  });

  // Factory method to create a DuLieuMauNguoiLaoDong instance from a JSON object
  factory DuLieuMauNguoiLaoDong.fromJson(Map<String, dynamic> json) {
    return DuLieuMauNguoiLaoDong(
      cauLaoDongTTLDID: json['CauLaoDongTTLDID'],
      cauLaoDongID: json['CauLaoDongID'],
      cauLaoDongMauID: json['CauLaoDongMauID'],
      cauLaoDongMauCode: json['CauLaoDongMauCode'],
      thuTu: json['ThuTu'],
      chiTieu: json['ChiTieu'],
      chiTieu1: json['ChiTieu1'],
      soNguoi: json['SoNguoi'],
      inDam: json['InDam'],
      inNghien: json['InNghien'],
      chiDoc: json['ChiDoc'],
      chuoiSQL: json['ChuoiSQL'],
      congThuc: json['CongThuc'],
      nganhNgheID: json['NganhNgheID'],
      stt: (json['STT'] as num).toDouble(),
    );
  }

  // Method to convert a DuLieuMauNguoiLaoDong instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'CauLaoDongTTLDID': cauLaoDongTTLDID,
      'CauLaoDongID': cauLaoDongID,
      'CauLaoDongMauID': cauLaoDongMauID,
      'CauLaoDongMauCode': cauLaoDongMauCode,
      'ThuTu': thuTu,
      'ChiTieu': chiTieu,
      'ChiTieu1': chiTieu1,
      'SoNguoi': soNguoi,
      'InDam': inDam,
      'InNghien': inNghien,
      'ChiDoc': chiDoc,
      'ChuoiSQL': chuoiSQL,
      'CongThuc': congThuc,
      'NganhNgheID': nganhNgheID,
      'STT': stt,
    };
  }
}
