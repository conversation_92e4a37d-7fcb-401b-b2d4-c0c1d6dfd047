// ignore_for_file: public_member_api_docs, sort_constructors_first
class ToChuc {
  String toChucID;
  String maToChuc;
  String tenToChuc;
  String tenKCN;
  String soDKKD;
  String tinhTrangTGHDKTID;
  String email;
  String soDienThoai;
  String nganhKinhTeID;
  String soCCCD;
  String soNha;
  String maSoThue;
  String diaChiCuThe;
  String tenNguoiSuDungLD;
  String tinh;
  String huyen;
  String xa;
  String thon;
  String loaiHinhDNID;
  String diaBanHCIDTinh;
  String diaBanHCIDHuyen;
  String diaBanHCIDXa;
  String diaBanHCIDThon;
  bool namTrongKCN;
  String ngayHoatDong;

  ToChuc({
    required this.toChucID,
    required this.maToChuc,
    required this.tenToChuc,
    required this.tenKCN,
    required this.soDKKD,
    required this.tinhTrangTGHDKTID,
    required this.email,
    required this.soDienT<PERSON><PERSON>,
    required this.nganhKinhTeID,
    required this.soCCCD,
    required this.soNha,
    required this.ma<PERSON><PERSON><PERSON>hu<PERSON>,
    required this.diaChiCuThe,
    required this.tenNguoiSuDungLD,
    required this.tinh,
    required this.huyen,
    required this.xa,
    required this.thon,
    required this.loaiHinhDNID,
    required this.diaBanHCIDTinh,
    required this.diaBanHCIDHuyen,
    required this.diaBanHCIDXa,
    required this.diaBanHCIDThon,
    required this.namTrongKCN,
    required this.ngayHoatDong,
  });

  factory ToChuc.fromJson(Map<String, dynamic> json) {
    return ToChuc(
      toChucID: json['ToChucID'] ?? '',
      maToChuc: json['MaToChuc'] ?? '',
      tenToChuc: json['TenToChuc'] ?? '',
      soDKKD: json['SoDKKD'] ?? '',
      tenKCN: json['TenKCN'] ?? '',
      email: json['Email'] ?? '',
      soDienThoai: json['SoDienThoai'] ?? '',
      nganhKinhTeID: json['NganhKinhTeID'] ?? '',
      soCCCD: json['SoCCCD'] ?? '',
      soNha: json['SoNha'] ?? '',
      maSoThue: json['MaSoThue'] ?? '',
      diaChiCuThe: json['DiaChiCuThe'] ?? '',
      tenNguoiSuDungLD: json['TenNguoiSuDungLD'] ?? '',
      tinh: json['Tinh'] ?? '',
      huyen: json['Huyen'] ?? '',
      xa: json['Xa'] ?? '',
      thon: json['Thon'] ?? '',
      loaiHinhDNID: json['LoaiHinhDNID'] ?? '',
      tinhTrangTGHDKTID: json['TinhTrangTGHDKTID'] ?? '',
      diaBanHCIDTinh: json['DiaBanHCID_Tinh'] ?? '',
      diaBanHCIDHuyen: json['DiaBanHCID_Huyen'] ?? '',
      diaBanHCIDXa: json['DiaBanHCID_Xa'] ?? '',
      diaBanHCIDThon: json['DiaBanHCID_Thon'] ?? '',
      namTrongKCN: json['NamTrongKCN'] ?? false,
      ngayHoatDong: json['NgayHoatDong'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ToChucID': toChucID,
      'MaToChuc': maToChuc,
      'TenToChuc': tenToChuc,
      'TenKCN': tenKCN,
      'SoDKKD': soDKKD,
      'Email': email,
      'SoDienThoai': soDienThoai,
      'NganhKinhTeID': nganhKinhTeID,
      'SoCCCD': soCCCD,
      'SoNha': soNha,
      'MaSoThue': maSoThue,
      'DiaChiCuThe': diaChiCuThe,
      'TenNguoiSuDungLD': tenNguoiSuDungLD,
      'Tinh': tinh,
      'Huyen': huyen,
      'Xa': xa,
      'Thon': thon,
      'LoaiHinhDNID': loaiHinhDNID,
      'TinhTrangTGHDKTID': tinhTrangTGHDKTID,
      'DiaBanHCID_Tinh': diaBanHCIDTinh,
      'DiaBanHCID_Huyen': diaBanHCIDHuyen,
      'DiaBanHCID_Xa': diaBanHCIDXa,
      'DiaBanHCID_Thon': diaBanHCIDThon,
      'NamTrongKCN': namTrongKCN,
      'NgayHoatDong': ngayHoatDong,
    };
  }
}
