import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/du_lieu_mau_nhu_cau_tuyen_dung.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/presentation/controller/cau_lao_dong_bien_dong_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class BuildForm3 extends GetView<CauLaoDongBienDongController> {
  const BuildForm3({super.key});
  @override
  Widget build(BuildContext context) {
    List<String> rowTitles = [
      "Mã nghề cấp 2",
      "Tên nghề nghiệp",
      "<PERSON><PERSON> lượng \n(người)",
      "Số lượng nữ \n(người)",
    ];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
            child: TextWidget(
              text: "3. Thông tin nhu cầu tuyển dụng",
              size: AppDimens.subText,
              fontWeight: FontWeight.w700,
            )),
        SingleChildScrollView(
          child: Obx(
            () => Table(
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                columnWidths: {
                  0: FixedColumnWidth(13.w), // Cột 0 (2 ký tự)
                  1: FixedColumnWidth(47.w), // Cột 1 (còn lại)
                  2: FixedColumnWidth(20.w), // Cột 2 (2 ký tự)
                  3: FixedColumnWidth(20.w), // Cột 3 (2 ký tự)
                },
                children: [
                  TableRow(
                    decoration: const BoxDecoration(
                      color: Color(0xffEAFFF9),
                    ),
                    children: rowTitles
                        .map((title) => _buildHeaderRow(title))
                        .toList(),
                  ),
                  ..._buildTableRows(controller.allDuLieuMauNhuCauTuyenDung),
                ]),
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderRow(String text) {
    return Container(
        padding: EdgeInsets.all(1.h),
        child: TextWidget(
          text: text,
          textAlign: TextAlign.center,
          size: AppDimens.subText,
          fontWeight: FontWeight.w500,
          color: AppColors.black,
        ));
  }

  List<TableRow> _buildTableRows(List<DuLieuMauNhuCauTuyenDung> data) {
    return data.asMap().entries.map((entry) {
      bool isLastRow = entry.key == data.length - 1;
      return TableRow(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isLastRow ? Colors.transparent : AppColors.grey,
              width: 0.5,
            ),
          ),
        ),
        children: [
          _buildCell(entry.value.ngheNghiepCLDCode),
          _buildCell(entry.value.tenNgheNghiepCLD),
          _buildInput(
            'TongSo',
            entry.value.tongSo.toInt().toString(),
            false,
            entry.value.cauLaoDongTDID,
          ),
          _buildInput(
            'SoLuongNu',
            entry.value.soLuongNu.toInt().toString(),
            false,
            entry.value.cauLaoDongTDID,
          ),
        ],
      );
    }).toList();
  }

  Widget _buildCell(String text) {
    return Container(
      padding: EdgeInsets.all(2.h),
      child: Text(
        text,
        textAlign: TextAlign.left,
        style: TextStyle(fontSize: AppDimens.subText, color: AppColors.black),
      ),
    );
  }

  Widget _buildInput(String tenCot, String thoiDiem, bool onlyRead, String id) {
    //, String id, String stt, String chiTieu
    if (num.tryParse(thoiDiem) == null) {
      return _buildCell(thoiDiem);
    } else {
      TextEditingController inputController =
          TextEditingController(text: thoiDiem);
      FocusNode focusNode1 = FocusNode();
      String oldSoNguoi = '0';
      focusNode1.addListener(() {
        if (focusNode1.hasFocus) {
          oldSoNguoi = inputController.text;
        } else {
          controller.onUnFocusInputForm3(
              tenCot, id, inputController, oldSoNguoi);
        }
      });

      return Container(
        padding: EdgeInsets.symmetric(horizontal: 3.w),
        height: 4.h,
        width: 20.w,
        alignment: Alignment.center,
        child: TextField(
          enabled: !onlyRead,
          controller: inputController,
          focusNode: focusNode1,
          textAlignVertical: TextAlignVertical.center,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            filled: onlyRead,
            fillColor: AppColors.grey2,
            contentPadding: EdgeInsets.symmetric(horizontal: 2.w),
            enabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: AppColors.grey),
            ),
            disabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: AppColors.grey),
            ),
            border: const OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: AppColors.grey),
            ),
          ),
        ),
      );
    }
  }
}
