import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textarea.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textfield.dart';
import 'package:attp_2024/core/ui/widgets/signature/signature_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/file_picker.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/presentation/controller/cau_lao_dong_bien_dong_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class BuildForm4 extends GetView<CauLaoDongBienDongController> {
  const BuildForm4({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildContentComponent(),
        _buildSignatureComponent(),
      ],
    );
  }

  Widget _buildTitle(IconData icon, String title) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.w),
      color: AppColors.grey2,
      child: Row(
        spacing: 2.w,
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.gray3,
          ),
          TextWidget(
            text: title,
            fontWeight: FontWeight.w700,
            size: AppDimens.defaultText,
            color: AppColors.black,
          ),
        ],
      ),
    );
  }

  Widget _buildSignatureComponent() {
    return Column(children: [
      _buildTitle(Icons.border_color, "Ký tên"),
      Padding(
        padding: EdgeInsets.all(3.w),
        child: Column(
          spacing: 2.w,
          children: [
            Obx(
              () => SignatureWidget(
                onSaved: (value) {
                  controller.chuKyPath.value = value;
                  log('Đường dẫn chữ ký: ${controller.chuKyPath.value}');
                },
                errorText: controller.errorChuKy.value,
              ),
            ),
            Obx(
              () => TextFieldWidget(
                isRequired: true,
                title: 'Người ký',
                placeholder: 'Nhập tên người ký',
                initialValue: controller.nguoiKy.value,
                onChange: (value) {
                  controller.nguoiKy.value = value;
                },
                errorWidget: controller.errorNguoiKy.value,
              ),
            ),
          ],
        ),
      ),
    ]);
  }

  Widget _buildContentComponent() {
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(Icons.library_books_sharp, "Nội dung"),
          Padding(
            padding: EdgeInsets.all(3.w),
            child: Column(spacing: 2.w, children: [
              Obx(
                () => TextAreaWidget(
                    title: 'Nội dung thu thập',
                    initialValue: controller.noiDungForm4.value,
                    placeholder: 'Nhập nội dung ...',
                    cornerRadius: 5,
                    onChange: (value) {
                      controller.noiDungForm4.value = value;
                    }),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  GestureDetector(
                    onTap: () {
                      if (controller.selectedFiles.length < 5) {
                        pickImagesAndPdf(
                            maxFiles: 5,
                            onFilesPicked: (files) {
                              controller.addFiles(files);
                            });
                      } else {
                        SnackbarUtil.showWarning(
                            'Bạn chỉ được chọn tối đa 5 tệp.');
                      }
                    },
                    child: Container(
                      // width: 44.w,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: AppColors.blue1),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(CupertinoIcons.paperclip,
                              color: AppColors.white),
                          SizedBox(
                            width: 5,
                          ),
                          TextWidget(
                            text: "Chọn tệp",
                            color: AppColors.white,
                          )
                        ],
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      if (controller.selectedFiles.length < 5) {
                        controller.pickImageFromCamera();
                      } else {
                        SnackbarUtil.showWarning(
                            'Bạn chỉ được chọn tối đa 5 tệp.');
                      }
                    },
                    child: Container(
                      // width: 44.w,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: AppColors.green2),
                      child: const Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(CupertinoIcons.camera, color: AppColors.white),
                            SizedBox(
                              width: 5,
                            ),
                            TextWidget(
                              text: "Chụp ảnh",
                              color: AppColors.white,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const Row(
                children: [
                  TextWidget(
                    text: "Tệp đã chọn (tối đa 5):",
                    textAlign: TextAlign.start,
                  ),
                ],
              ),
              Row(
                children: [
                  Obx(() {
                    if (controller.selectedFiles.isEmpty) {
                      return const SizedBox(); // Không hiển thị gì nếu danh sách rỗng
                    }
                    return SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        spacing: 2.w,
                        children: controller.selectedFiles.map((file) {
                          final fileName = file.split('/').last; // Lấy tên file
                          final isImage = fileName.endsWith('.jpg') ||
                              fileName.endsWith('.png');
                          final isVideo = fileName.endsWith('.mp4');
                          return Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 5.0),
                            child: Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: AppColors.primary),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Icon(
                                    isImage
                                        ? Icons.image
                                        : isVideo
                                            ? Icons.videocam
                                            : Icons.insert_drive_file,
                                    size: 50,
                                    color: Colors.grey,
                                  ),
                                ),
                                // Text(
                                //   fileName,
                                //   style: const TextStyle(fontSize: 12),
                                //   overflow: TextOverflow.ellipsis,
                                // ),
                                IconButton(
                                  icon: const Icon(Icons.close,
                                      size: 18, color: Colors.red),
                                  onPressed: () {
                                    controller.removeFile(file);
                                  },
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    );
                  }),
                ],
              ),
            ]),
          ),
        ]);
  }
}
