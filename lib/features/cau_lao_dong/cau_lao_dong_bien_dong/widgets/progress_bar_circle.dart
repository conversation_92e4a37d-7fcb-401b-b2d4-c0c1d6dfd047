import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class StepProgressWidget extends StatelessWidget {
  final int currentStep;
  final int totalStep;
  final String nameStep;

  const StepProgressWidget({
    super.key,
    required this.currentStep,
    required this.totalStep,
    required this.nameStep,
  });

  @override
  Widget build(BuildContext context) {
    double progress = currentStep / totalStep;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      spacing: 5.w,
      children: [
        SizedBox(
          width: 7.w,
          height: 7.w,
          child: CircularProgressIndicator(
            value: progress,
            backgroundColor: const Color.fromARGB(30, 0, 0, 0),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            strokeWidth: 5.0,
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              nameStep,
              style: TextStyle(
                fontSize: AppDimens.defaultText,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Text(
              'Bước $currentStep/$totalStep',
              style: TextStyle(
                fontSize: AppDimens.subText,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
