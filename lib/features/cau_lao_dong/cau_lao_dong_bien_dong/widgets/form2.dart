import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/models/du_lieu_mau_nguoi_lao_dong.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/presentation/controller/cau_lao_dong_bien_dong_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class BuildForm2 extends GetView<CauLaoDongBienDongController> {
  const BuildForm2({super.key});
  @override
  Widget build(BuildContext context) {
    List<String> rowTitles = ["TT", "Chỉ tiêu", "<PERSON><PERSON> lượng (người)"];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
            child: TextWidget(
              text: "2. Thông tin người lao động đang làm việc",
              size: AppDimens.subText,
              fontWeight: FontWeight.w700,
            )),
        SingleChildScrollView(
          child: Obx(
            () => Table(
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              columnWidths: {
                0: FixedColumnWidth(15.w),
                1: FixedColumnWidth(55.w),
                2: FixedColumnWidth(30.w),
              },
              children: [
                TableRow(
                  decoration: const BoxDecoration(
                    color: Color(0xffEAFFF9),
                  ),
                  children:
                      rowTitles.map((title) => _buildHeaderRow(title)).toList(),
                ),
                ..._buildTableRows(controller.allDuLieuMauNguoiLaoDong),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderRow(String text) {
    return Container(
        padding: EdgeInsets.all(1.h),
        child: TextWidget(
          text: text,
          textAlign: TextAlign.center,
          size: AppDimens.subText,
          fontWeight: FontWeight.w500,
          color: AppColors.black,
        ));
  }

  List<TableRow> _buildTableRows(List<DuLieuMauNguoiLaoDong> data) {
    return data.asMap().entries.map((entry) {
      bool isLastRow = entry.key == data.length - 1;
      return TableRow(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isLastRow
                  ? Colors.transparent
                  : const Color.fromARGB(255, 197, 197, 197),
              width: 0.5,
            ),
          ),
        ),
        children: [
          SizedBox(
            width: 15.w,
            child: _buildCell((entry.value.thuTu).toString(),
                entry.value.chiDoc, entry.value.inDam),
          ),
          SizedBox(
            width: 20.w,
            child: _buildCell(
                entry.value.chiTieu, entry.value.chiDoc, entry.value.inDam),
          ),
          SizedBox(
            width: 10.w,
            child: _buildInput(
                entry.value.soNguoi,
                entry.value.chiDoc,
                entry.value.cauLaoDongTTLDID,
                entry.value.thuTu,
                entry.value.chiTieu),
          ),
        ],
      );
    }).toList();
  }

  Widget _buildCell(String text, bool readOnly, bool indam) {
    return Container(
      padding: EdgeInsets.all(2.h),
      child: Text(
        text,
        textAlign: TextAlign.left,
        style: TextStyle(
            fontSize: AppDimens.subText,
            color: AppColors.black,
            fontWeight: indam ? FontWeight.w600 : FontWeight.normal),
      ),
    );
  }

  Widget _buildInput(
      String thoiDiem, bool onlyRead, String id, String stt, String chiTieu) {
    if (num.tryParse(thoiDiem) == null) {
      return _buildCell(thoiDiem, onlyRead, false);
    } else {
      TextEditingController inputController =
          TextEditingController(text: thoiDiem);
      FocusNode focusNode = FocusNode();
      String oldSoNguoi = '0';
      focusNode.addListener(() {
        if (focusNode.hasFocus) {
          oldSoNguoi = inputController.text;
        } else {
          log(inputController.text);
          controller.onUnFocusInputForm2(
              id, stt, chiTieu, inputController, oldSoNguoi);
        }
      });

      return Container(
        padding: EdgeInsets.only(left: 6.w, right: 3.w),
        height: 4.h,
        width: 20.w,
        alignment: Alignment.center,
        child: TextField(
          enabled: !onlyRead,
          controller: inputController,
          focusNode: focusNode,
          textAlignVertical: TextAlignVertical.center,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            filled: onlyRead,
            fillColor: AppColors.grey2,
            contentPadding: EdgeInsets.symmetric(horizontal: 2.w),
            enabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: AppColors.grey),
            ),
            disabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: AppColors.grey),
            ),
            border: const OutlineInputBorder(
              borderSide: BorderSide(width: 1, color: AppColors.grey),
            ),
          ),
        ),
      );
    }
  }
}
