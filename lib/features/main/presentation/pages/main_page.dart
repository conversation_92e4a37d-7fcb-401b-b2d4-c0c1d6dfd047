import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
import 'package:attp_2024/features/main/presentation/widgets/customs_navigation_bar_v1.dart';

class MainPage extends GetView<MainController> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Navigator(
            key: Get.nestedKey(10),
            initialRoute: "/home",
            onGenerateRoute: controller.onGenerateRoute,
          ),
          Positioned(
            right: 10,
            bottom: 80,
            child: GestureDetector(
              onTap: () {
                Get.toNamed(Routes.chatbot);
              },
              child: Image.asset(
                AppImageString.iChatBot,
                width: 50,
                height: 50,
              ),
            ),
          ),
          const Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: BottomNavigationBarV1(),
          ),
        ],
      ),
    );
  }
}
