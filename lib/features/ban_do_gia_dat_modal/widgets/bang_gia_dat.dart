import 'dart:convert';
import 'dart:developer';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class BGDTable extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  const BGDTable({super.key, required this.data});

  @override
  State<BGDTable> createState() => _BGDTableState();
}

class _BGDTableState extends State<BGDTable> {
  bool isExpand = false;
  String defaultText = 'Chưa tìm thấy dữ liệu';

  // Lấy danh sách header động từ trường "LoaiDat"
  List<String> getLoaiDatHeaders() {
    return widget.data.map((e) => e['LoaiDat'] as String).toList();
  }

  // Lấy tập hợp các đơn vị hành chính (ở đây sử dụng "Xa") từ tất cả các nhóm
  List<String> getDonViHanhChinh() {
    Set<String> units = {};
    for (var group in widget.data) {
      List<dynamic> bangGia = group['DataBangGiaConLai'] ?? [];
      for (var item in bangGia) {
        // Bạn có thể kết hợp các trường Tinh, Huyen, Xa nếu cần
        units.add(item['Xa'] as String);
      }
    }
    // Sắp xếp theo tên đơn vị nếu cần
    List<String> unitList = units.toList();
    unitList.sort();
    return unitList;
  }

  // Lấy giá bán của loại đất cho một đơn vị hành chính cụ thể
  String getGiaBan(String loaiDat, String adminUnit) {
    log(loaiDat, name: 'giaban_loaidat');
    log(adminUnit, name: 'giaban_adminunit');
    try {
      final group = widget.data.firstWhere(
        (element) => element['LoaiDat'] == loaiDat,
      );
      log(group.toString(), name: 'giaban_group');
      List<dynamic> bangGia = group['DataBangGiaConLai'] ?? [];
      // Tìm bản ghi có đơn vị hành chính khớp
      var found = bangGia.firstWhere(
        (item) => item['Xa'] == adminUnit,
      );
      return (jsonDecode(found['GiaBan'])[0].toString());
    } catch (e) {
      // Nếu không tìm thấy group hoặc bản ghi nào, trả về defaultText
      return defaultText;
    }
  }

  @override
  Widget build(BuildContext context) {
    List<String> headers = getLoaiDatHeaders();
    List<String> donViHanhChinh = getDonViHanhChinh();
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  isExpand = !isExpand;
                });
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: TextWidget(
                  text:
                      '☛  Bảng giá đất còn lại tại: thành phố Vị Thanh (gán cứng!)',
                  color: Colors.cyan,
                  size: AppDimens.smallText,
                  fontStyle: FontStyle.italic,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: isExpand
              ? _clickExpandTable(headers, donViHanhChinh)
              : const SizedBox(),
        ),
      ],
    );
  }

  Widget _clickExpandTable(List<String> headers, List<String> donViHanhChinh) {
    return Row(
      children: [
        // Cột đầu tiên cố định
        Table(
          border: TableBorder.all(color: Colors.black, width: 1),
          columnWidths: const {
            0: FixedColumnWidth(45),
            1: FixedColumnWidth(150),
          },
          children: [
            TableRow(
              decoration: BoxDecoration(color: Colors.grey[300]),
              children: [
                _tableHeader('STT'),
                _tableHeader('Tên đơn vị hành chính'),
              ],
            ),
            ...List.generate(donViHanhChinh.length, (index) {
              return TableRow(
                children: [
                  _tableCell('${index + 1}', alignment: TextAlign.center),
                  _tableCell(donViHanhChinh[index],
                      alignment: TextAlign.center),
                ],
              );
            }),
          ],
        ),
        // Các cột còn lại có thể cuộn
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Table(
              border: TableBorder.all(color: Colors.black, width: 1),
              defaultColumnWidth: const FixedColumnWidth(150),
              children: [
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey[300]),
                  children: headers.map((h) => _tableHeader(h)).toList(),
                ),
                ...donViHanhChinh.map((unit) {
                  return TableRow(
                    children: headers.map((loaiDat) {
                      return _tableCell(getGiaBan(loaiDat, unit),
                          alignment: TextAlign.center);
                    }).toList(),
                  );
                }),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _tableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextWidget(
        text: text,
        color: Colors.black,
        size: AppDimens.smallText,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _tableCell(String text, {TextAlign? alignment}) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        textAlign: alignment ?? TextAlign.left,
      ),
    );
  }
}
