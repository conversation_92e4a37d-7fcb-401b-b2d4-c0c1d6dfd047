// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';
import 'dart:developer';

import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/ban_do_gia_dat_modal/presentation/controller/ban_do_gia_dat_controller.dart';
import 'package:attp_2024/features/ban_do_gia_dat_modal/widgets/title.dart';

class CardWidget1 extends StatefulWidget {
  final String title;
  final List<dynamic> data;
  CardWidget1({
    super.key,
    required this.title,
    required this.data,
  });

  @override
  State<CardWidget1> createState() => _CardWidget1State();
}

class _CardWidget1State extends State<CardWidget1> {
  bool isExpand = false;
  String defaultText = 'Chưa có dữ liệu';
  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          Container(
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4.0),
                topRight: Radius.circular(4.0),
              ),
              color: AppColors.gray3,
            ),
            padding: EdgeInsets.symmetric(horizontal: 2.h, vertical: 0.5.h),
            child: TextWidget(
              text: widget.title,
              size: AppDimens.smallText,
              color: AppColors.white,
            ),
          ),
        ],
      ),
      GestureDetector(
        onTap: () {
          setState(() {
            isExpand = !isExpand;
          });
        },
        child: Container(
          width: 100.w,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.gray2, width: 0.5),
          ),
          child: Padding(
            padding: EdgeInsets.all(3.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  text: widget.data.first['DoanDuong'] ?? defaultText,
                  size: AppDimens.smallText,
                  color: const Color.fromARGB(255, 0, 5, 99),
                  fontWeight: FontWeight.bold,
                ),
                title1(
                  'Loại đô thị: ',
                  widget.data.first['LoaiDoThi'] ?? defaultText,
                  styleContent: TextStyle(
                    color: Colors.cyan,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                title1(
                  'Loại đất: ',
                  widget.data.first['LoaiDat'] ?? defaultText,
                  styleContent: TextStyle(
                    color: Colors.indigo,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                AnimatedSize(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  child: isExpand ? _clickExpandTable() : const SizedBox(),
                ),
              ],
            ),
          ),
        ),
      ),
    ]);
  }

  Widget _clickExpandTable() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 1.5.h),
          child: const DottedLine(
            direction: Axis.horizontal,
            lineLength: double.infinity,
            lineThickness: 0.7,
            dashLength: 6.0,
            dashColor: AppColors.gray3,
            dashGapLength: 5.0,
            dashGapColor: Colors.transparent,
          ),
        ),
        Row(
          children: [
            // Cột đầu tiên cố định
            Table(
              border: TableBorder.all(color: Colors.black, width: 1),
              columnWidths: const {
                0: FixedColumnWidth(150),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey[300]),
                  children: [
                    tableHeader(''),
                  ],
                ),
                ...widget.data.map((e) {
                  List<dynamic> tyLeDC =
                      e['TyLeDC'] != null ? json.decode(e['TyLeDC']) : [];
                  return TableRow(children: [
                    tableCell(e['ThoiDiem'] ?? '',
                        alignment: TextAlign.center,
                        hasPercent: tyLeDC.isNotEmpty && tyLeDC[0] != null),
                  ]);
                })
              ],
            ),
            // Các cột còn lại có thể cuộn
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Table(
                  border: TableBorder.all(color: Colors.black, width: 1),
                  columnWidths: const {
                    0: FixedColumnWidth(150),
                    1: FixedColumnWidth(150),
                    2: FixedColumnWidth(150),
                    3: FixedColumnWidth(150),
                  },
                  children: [
                    TableRow(
                      decoration: BoxDecoration(color: Colors.grey[300]),
                      children: [
                        tableHeader('VT1'),
                        tableHeader('VT2'),
                        tableHeader('VT3'),
                        tableHeader('VT4'),
                      ],
                    ),
                    ...widget.data.map((e) {
                      List<dynamic> giaBan = json.decode(e['GiaBan']);
                      List<dynamic> tyLeDC =
                          e['TyLeDC'] != null ? json.decode(e['TyLeDC']) : [];

                      String getGiaBanValue(int index) =>
                          (giaBan.length > index &&
                                  giaBan[index]?.isNotEmpty == true)
                              ? giaBan[index].toString()
                              : '';

                      double getTyLeDCValue(int index) {
                        if (tyLeDC.length > index &&
                            tyLeDC[index]?.isNotEmpty == true) {
                          double? result = double.tryParse(tyLeDC[index] ?? '');
                          return result ?? 0.0;
                        }
                        return 0.0;
                      }

                      return TableRow(
                        children: List.generate(
                          4,
                          (index) => tableCell(getGiaBanValue(index),
                              alignment: TextAlign.center,
                              tyLeDC: getTyLeDCValue(index),
                              hasPercent: tyLeDC.isNotEmpty &&
                                  tyLeDC[index]?.isNotEmpty == true,
                              isDate: true),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget tableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        style: const TextStyle(fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget tableCell(String text,
      {TextAlign? alignment,
      double? tyLeDC,
      bool? hasPercent,
      bool? isDate = false}) {
    int percent = (((tyLeDC ?? 0.0) - 1) * 100).ceil();

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: hasPercent == true
          ? Column(
              children: [
                Text(
                  text,
                  textAlign: alignment ?? TextAlign.left,
                  style: TextStyle(
                    color: hasPercent == true && isDate == true
                        ? Colors.amber[800]
                        : Colors.black,
                  ),
                ),
                isDate == true
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 1.5.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: percent > 0 ? Colors.green : Colors.red,
                            ),
                            child: Row(
                              spacing: 0.3.w,
                              children: [
                                Text(
                                  '$percent%',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                                percent > 0
                                    ? Icon(
                                        CupertinoIcons.arrow_up_circle_fill,
                                        color: Colors.white,
                                        size: 13,
                                      )
                                    : Icon(
                                        CupertinoIcons.arrow_down_circle_fill,
                                        color: Colors.white,
                                        size: 13,
                                      ),
                              ],
                            ),
                          ),
                        ],
                      )
                    : Row(children: [
                        SizedBox(
                          height: 16.5,
                        )
                      ])
              ],
            )
          : Text(
              text,
              textAlign: alignment ?? TextAlign.left,
            ),
    );
  }
}
