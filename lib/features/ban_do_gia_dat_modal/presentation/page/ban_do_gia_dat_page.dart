import 'dart:developer';

import 'package:attp_2024/features/ban_do_gia_dat_modal/presentation/controller/ban_do_gia_dat_controller.dart';
import 'package:attp_2024/features/ban_do_gia_dat_modal/widgets/bang_gia_dat.dart';
import 'package:attp_2024/features/ban_do_gia_dat_modal/widgets/card.dart';
import 'package:attp_2024/features/ban_do_gia_dat_modal/widgets/title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class SalesData {
  SalesData(this.year, this.sales);
  final String year;
  final double sales;
}

class BanDoGiaDatModalPage extends GetView<BanDoGiaDatModalController> {
  const BanDoGiaDatModalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: const AppBarWidget(
          title: "Bản đồ giá đất",
          titleSize: 18,
        ),
        body: _buildBody());
  }

  Widget _buildBody() {
    return Container(
      padding: const EdgeInsets.all(AppDimens.sizeText),
      child: Column(
        children: [
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              minimumSize: Size(100.w, 6.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimens.sizeText),
              ),
            ),
            onPressed: () {
              showModalBottomSheet(
                context: Get.context!,
                isScrollControlled: true,
                builder: (BuildContext context) {
                  return FractionallySizedBox(
                    heightFactor: 0.9, // Chiếm 90% chiều cao màn hình
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                      child: Column(
                        spacing: 1.h,
                        children: <Widget>[
                          _shortLine(),
                          title1(
                            'Bảng giá đất tại đoạn đường: ',
                            'Cầu Xà No - Giáp ranh Huyện Vị Thủy',
                            styleTitle: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          _twiceButton(),
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(spacing: 1.h, children: [
                                Obx(() {
                                  final data = controller.dataTraCuu;
                                  if (data.isEmpty) {
                                    return const SizedBox();
                                  }

                                  return Column(
                                    spacing: 1.h,
                                    children: data
                                        .map(
                                          (item) => CardWidget1(
                                            title: item['CoQuan'].toString(),
                                            data: item['DataTraCuu'],
                                          ),
                                        )
                                        .toList(),
                                  );
                                }),
                                BGDTable(data: controller.dataBangGiaConLai),
                                _buildChart(),
                              ]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
            child: const TextWidget(
              text: "Modal 1",
              color: Colors.white,
            ),
          ),
          SizedBox(height: 2.h),
          //
        ],
      ),
    );
  }

  Widget _buildChart() {
    return Column(
      children: [
        Row(
          children: [
            Icon(Icons.bar_chart_outlined),
            TextWidget(
              text: 'Biểu đồ giá đất tại đô thị qua các thời điểm',
              fontWeight: FontWeight.bold,
              size: AppDimens.smallText,
            )
          ],
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: SfCartesianChart(
            legend: Legend(isVisible: true, position: LegendPosition.top),
            primaryXAxis: DateTimeAxis(
              dateFormat: DateFormat('dd/MM/yyyy'),
              intervalType: DateTimeIntervalType.years,
            ),
            primaryYAxis: NumericAxis(
              numberFormat: NumberFormat.decimalPattern(),
              majorGridLines: const MajorGridLines(
                width: 1, // Độ dày đường kẻ
                color: Colors.grey, // Màu đường kẻ
                dashArray: [
                  5,
                  5
                ], // Dạng nét đứt (nếu thích), bỏ nếu muốn nét liền
              ),
            ),
            series: <CartesianSeries>[
              LineSeries<ChartData, DateTime>(
                name: 'Thị trường',
                dataSource: controller.data_chart,
                xValueMapper: (ChartData data, _) => data.year,
                yValueMapper: (ChartData data, _) => data.marketValue,
                markerSettings: const MarkerSettings(
                    isVisible: true, color: Colors.brown, height: 5, width: 5),
                dataLabelSettings: DataLabelSettings(
                  isVisible: true, // Hiển thị giá trị
                  labelAlignment: ChartDataLabelAlignment
                      .top, // Vị trí của label (top, bottom, middle,...)
                  textStyle: TextStyle(
                    fontSize: 11.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                color: Colors.brown,
              ),
              LineSeries<ChartData, DateTime>(
                name: 'Ủy ban nhân dân',
                dataSource: controller.data_chart,
                xValueMapper: (ChartData data, _) => data.year,
                yValueMapper: (ChartData data, _) => data.governmentValue,
                markerSettings: const MarkerSettings(
                    isVisible: true, color: Colors.blue, height: 5, width: 5),
                dataLabelSettings: DataLabelSettings(
                  isVisible: true, // Hiển thị giá trị
                  labelAlignment: ChartDataLabelAlignment
                      .top, // Vị trí của label (top, bottom, middle,...)
                  textStyle: TextStyle(
                    fontSize: 11.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                color: Colors.blue,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _shortLine() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      width: 20.w,
      height: 0.5.h,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(20),
      ),
    );
  }

  Widget _twiceButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 3.h, vertical: 1.h),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary),
          borderRadius: BorderRadius.circular(5.0),
        ),
        child: Row(
          children: [
            Expanded(
              child: Obx(() => InkWell(
                    onTap: () {
                      controller.selectedTab.value = 0;
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: controller.selectedTab.value == 0
                            ? AppColors.primary
                            : AppColors.white,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(4.0),
                          bottomLeft: Radius.circular(4.0),
                        ),
                      ),
                      child: Center(
                        child: TextWidget(
                          text: "Giá đất",
                          color: controller.selectedTab.value == 0
                              ? AppColors.white
                              : AppColors.primary,
                          size: AppDimens.smallText,
                        ),
                      ),
                    ),
                  )),
            ),
            Expanded(
              child: Obx(() => InkWell(
                    onTap: () {
                      controller.selectedTab.value = 1;
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: controller.selectedTab.value == 0
                            ? AppColors.white
                            : AppColors.primary,
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(4.0),
                          bottomRight: Radius.circular(4.0),
                        ),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 0.5.h),
                      child: Center(
                        child: TextWidget(
                          text: "Bản đồ số",
                          color: controller.selectedTab.value == 1
                              ? AppColors.white
                              : AppColors.primary,
                          size: AppDimens.smallText,
                        ),
                      ),
                    ),
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
