import 'dart:developer';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:get/get.dart';

class ChartData {
  final DateTime year;
  final int marketValue;
  final int governmentValue;

  ChartData(this.year, this.marketValue, this.governmentValue);
}

class BanDoGiaDatModalController extends GetxController {
  var isLoading = true.obs;
  var selectedTab = 0.obs;
  var isExpandThiTruong = false.obs;

  final List<ChartData> data_chart = [
    ChartData(DateTime(2020, 1, 1), 0, 1500000),
    ChartData(DateTime(2022, 1, 1), 900000, 1500000),
    ChartData(DateTime(2023, 1, 1), 900000, 2175000),
    ChartData(DateTime(2025, 3, 6), 900000, 2175000),
  ].obs;

  final coreService = CoreService();

  List<dynamic> dataCQ_ = [].obs;
  List<Map<String, dynamic>> dataTraCuu = <Map<String, dynamic>>[].obs;
  List<dynamic> dataLoaiDatCL = [].obs;
  List<Map<String, dynamic>> dataBangGiaConLai = <Map<String, dynamic>>[].obs;
  List<dynamic> dataChart = [].obs;
  List<dynamic> dataChart_ = [].obs;
  @override
  Future<void> onInit() async {
    super.onInit();
    fetchLoadDataCQ_();

    // fetchLoadDataChart('', '');

    fetchLoadLoaiDatCL('', '');
    fetchLoadBangGiaConLai('', '', '');
  }

  Future<void> fetchLoadDataCQ_() async {
    var response = await coreService.callProcData(
        proc: 'Proc_Mobile_LoadData_CQ_',
        fromJson: (json) => json,
        request: [
          {
            "value": "bee2ec67-d735-4194-a180-90c5f99728d2",
            "name": "DoanDuongID",
            "type": "guid"
          },
          {
            "value": "2b41c5c5-612a-4007-9703-2b9706babbcc",
            "name": "LoaiDatID",
            "type": "guid"
          },
          {
            "name": "UserGroupID",
            "type": "guid",
            "value": "11d0d446-ef1a-43f6-929f-b6fc32317301"
          }
        ]);
    if (response.status == Status.success) {
      log(response.data.toString(), name: 'fetchLoadDataCQ_');
      dataCQ_.assignAll(response.data ?? []);
      fetchAllDataTraCuu();
    }
  }

  Future<List<dynamic>> fetchLoadDataTraCuu(String coQuanCungCapTTID) async {
    log(coQuanCungCapTTID, name: 'slF');
    var response = await coreService.callProcData(
        proc: 'Proc_Mobile_DataTraCuu',
        fromJson: (json) => json,
        request: [
          {
            "name": "DoanDuongID",
            "type": "guid",
            "value": "bee2ec67-d735-4194-a180-90c5f99728d2"
          },
          {
            "name": "CoQuanCungCapTTID",
            "type": "guid",
            "value": coQuanCungCapTTID
          },
          {
            "name": "LoaiDatID",
            "value": "2b41c5c5-612a-4007-9703-2b9706babbcc",
            "type": "guid"
          },
          {
            "type": "guid",
            "name": "UserGroupID",
            "value": "11d0d446-ef1a-43f6-929f-b6fc32317301"
          }
        ]);
    if (response.status == Status.success) {
      return (response.data ?? []);
    }
    return [];
  }

  Future<void> fetchAllDataTraCuu() async {
    if (dataCQ_.isEmpty) return;

    List<Future<Map<String, dynamic>>> futures = [];

    for (var item in dataCQ_) {
      String coQuanCungCapTTID = item['CoQuanCungCapTTID'];
      String coQuanName = item['CoQuan'];

      // Thêm mỗi request vào danh sách Future
      futures.add(fetchLoadDataTraCuu(coQuanCungCapTTID).then((dataTraCuu) {
        return {
          "CoQuanCungCapTTID": coQuanCungCapTTID,
          "CoQuan": coQuanName,
          "DataTraCuu": dataTraCuu,
        };
      }));
    }
    // Chạy tất cả request song song
    List<Map<String, dynamic>> resultList = await Future.wait(futures);
    // log(resultList.toString(), name: 'fetchAllDataTraCuu');
    dataTraCuu.assignAll(resultList);
  }

//-------------------------------------BẢNG GIÁ ĐẤT ---------------------------
  Future<void> fetchLoadLoaiDatCL(String huyenID, String userGroupID) async {
    var response = await coreService.callProcData(
        proc: 'Proc_Mobile_LoadLoaiDatCL',
        fromJson: (json) => json,
        request: [
          {
            "value": "9B239644-9447-459B-B2BE-22A523E05B67",
            "name": "HuyenID",
            "type": "guid"
          },
          {
            "name": "UserGroupID",
            "type": "guid",
            "value": "11d0d446-ef1a-43f6-929f-b6fc32317301"
          }
        ]);
    if (response.status == Status.success) {
      // log(response.data.toString(), name: 'fetch___1');
      dataLoaiDatCL.assignAll(response.data ?? []);
      fetchAllDataBangGiaConLai();
    }
  }

  Future<List<dynamic>> fetchLoadBangGiaConLai(
      String tinhID, String huyenID, String loaiDatID) async {
    var response = await coreService.callProcData(
        proc: 'Proc_Mobile_BangGiaConLai',
        fromJson: (json) => json,
        request: [
          {"value": tinhID, "name": "Tinh", "type": "guid"},
          {"value": huyenID, "name": "Huyen", "type": "guid"},
          {"value": loaiDatID, "name": "LoaiDat", "type": "guid"}
        ]);
    if (response.status == Status.success) {
      return (response.data ?? []);
    }
    return [];
  }

  Future<void> fetchAllDataBangGiaConLai() async {
    if (dataLoaiDatCL.isEmpty) return;

    List<Future<Map<String, dynamic>>> futures = [];

    for (var item in dataLoaiDatCL) {
      String tinhID = item['TinhID'];
      String huyenID = item['HuyenID'];
      String loaiDatID = item['LoaiDatID'];

      // Thêm mỗi request vào danh sách Future
      futures.add(fetchLoadBangGiaConLai(tinhID, huyenID, loaiDatID)
          .then((dataBangGiaConLai) {
        return {
          "LoaiDat": item['LoaiDat'],
          "DataBangGiaConLai": dataBangGiaConLai,
        };
      }));
    }
    // Chạy tất cả request song song
    List<Map<String, dynamic>> resultList = await Future.wait(futures);
    log(resultList.toString(), name: 'fetchAllDataBangGiaConLai');
    dataBangGiaConLai.assignAll(resultList);
  }

  // --------------------------------------------------------------------------

  Future<void> fetchLoadDataChart(String ma, String loai) async {
    var response = await coreService.callProcData(
        proc: 'Proc_Mobile_LoadDataChart',
        fromJson: (json) => json,
        request: [
          {
            "type": "guid",
            "name": "ma",
            "value": "bee2ec67-d735-4194-a180-90c5f99728d2"
          },
          {
            "name": "loai",
            "type": "guid",
            "value": "2b41c5c5-612a-4007-9703-2b9706babbcc"
          }
        ]);
    if (response.status == Status.success) {
      log(response.data.toString(), name: 'fetchLoadDataChart');
      dataChart.assignAll(response.data ?? []);
    }
  }

  Future<void> fetchLoadDataChart_(
      String ma, String thoidiem, String coQuan, String loai) async {
    var response = await coreService.callProcData(
        proc: 'Proc_Mobile_LoadDataChart_',
        fromJson: (json) => json,
        request: [
          {"name": "ma", "type": "guid", "value": ma},
          {"name": "nam", "value": thoidiem, "type": "string"},
          {"name": "CQ", "type": "string", "value": coQuan},
          {"name": "loai", "type": "guid", "value": loai}
        ]);
    if (response.status == Status.success) {
      log(response.data.toString(), name: 'fetchLoadDataChart_');
      dataChart_.assignAll(response.data ?? []);
    }
  }
}
