class ChatResponse {
  final String message;
  final String type;
  final String url;
  final bool success;

  ChatResponse({
    required this.message,
    required this.success,
    required this.type,
    required this.url,
  });

  factory ChatResponse.fromJson(Map<String, dynamic> json) {
    return ChatResponse(
      message: json['message'] ?? '',
      success: json['success'] ?? false,
      type: json['type'] ?? 'text',
      url: json['url'] ?? '',
    );
  }

  factory ChatResponse.mockResponse(String question) {
    return ChatResponse(
      message: '<PERSON><PERSON>y là câu trả lời cho câu hỏi: "$question"',
      success: true,
      type: 'text',
      url: '',
    );
  }
}