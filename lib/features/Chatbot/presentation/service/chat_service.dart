import 'dart:async';
import 'package:attp_2024/core/services/auth_use_case/auth_use_case.dart';
import 'package:attp_2024/features/chatbot/model/request/chat_request.dart';
import 'package:attp_2024/features/chatbot/model/response/chat_response.dart';
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logger/logger.dart';

class ChatService {
  late Dio _dio;
  final logger = Logger();
  
  ChatService() {
    _initializeDio();
  }

  void _initializeDio() async {
    final baseurlApi =  dotenv.env['APICHAT'] ?? '';
    final token = await AuthUseCase.getTokenMemory();
    //print("baseUrl_API: $baseurlApi");
    //print("token: $token");
    _dio = Dio(BaseOptions(
      baseUrl: baseurlApi,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 15),
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer ${token.replaceAll('"', '')}",
      },
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        logger.d('Request: ${options.method} ${options.path}${options.queryParameters}');
        logger.d('Headers: ${options.headers}');
        return handler.next(options);
      },
      onResponse: (response, handler) {
        logger.d('Response: ${response.data}');
        return handler.next(response);
      },
      onError: (DioException error, handler) {
        logger.e('Error: ${error.message}');
        return handler.next(error);
      },
    ));
  }

  void updateToken(String token) {
    if (token.isNotEmpty) {
      _dio.options.headers["Authorization"] = "Bearer $token";
      logger.i('Token updated successfully');
    }
  }

  Future<ChatResponse> sendMessage(ChatRequest request) async {
    try {
      logger.i("ChatService - Sending request with message: ${request.message}");

      final response = await _dio.post(
        '/chat',
        queryParameters: {
          'message': request.message,
        },
      );

      logger.i("ChatService - Response received: ${response.data}");

      if (response.statusCode == 200) {
        return ChatResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }

    } catch (e) {
      logger.e("ChatService - Error: $e");
      throw Exception('Error sending message: $e');
    }
  }
}