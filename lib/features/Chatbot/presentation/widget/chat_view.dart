import 'dart:convert';
import 'dart:io';

import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/custom_textfield/widgets/custom_textfield.dart';
import 'package:attp_2024/features/chatbot/presentation/controller/chatbot_controller.dart';
import 'package:attp_2024/features/chatbot/presentation/widget/welcome_message_widget.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:mime/mime.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

class ChatViewWidget extends GetView<ChatBotController> {
  ChatViewWidget({
    Key? key,
  }) : super(key: key);

  final TextEditingController textController = TextEditingController();

  void _handleAttachmentPressed(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                controller.handleImageSelection((msg) {
                  controller.addMessage(msg);
                });
              },
              child: const Text('Photo'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                controller.handleFileSelection((msg) {
                  controller.addMessage(msg);
                });
              },
              child: const Text('File'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      print("ChatView - Messages length: ${controller.messages.length}");
      //final messagesList = controller.messages.toList().reversed.toList();
      return Chat(
        // messages: messagesList,
        messages: controller.messages,
        onAttachmentPressed: () => _handleAttachmentPressed(context),
        onMessageTap: (_, message) => controller.handleMessageTap(message),
        onSendPressed: controller.handleSendPressed,
        showUserAvatars: true,
        showUserNames: true,
        user: controller.user,

        theme: const DefaultChatTheme(
          messageInsetsHorizontal: 12,
          messageInsetsVertical: 10,
          messageBorderRadius: 15,
          bubbleMargin: EdgeInsets.fromLTRB(10.0, 5.0, 0.0, 0.0),
        ),
        customMessageBuilder: (message, {required messageWidth}) {
          if (message.metadata?['type'] == 'welcome_message') {
            return const WelcomeMessageWidget();
          }
          return const SizedBox();
        },
        customBottomWidget: _buildCustomInput(),
      );
    });
  }

  Widget _buildCustomInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 1,
          ),
        ],
      ),
      child: Row(
        children: [
          Obx(() {
            return IconButton(
              icon: Icon(
                controller.isListening.value ? Icons.mic : Icons.mic_none,
                color: controller.isListening.value
                    ? Colors.red
                    : AppColors.primary,
                size: 28,
              ),
              onPressed: () {
                controller.handleVoiceMessage((msg) {
                  controller.addMessage(msg);
                });
              },
            );
          }),
          Expanded(
            child: Obx(() => TextFieldWidget(
              title: '',
              placeholder: 'Nhập nội dung...',
              setValue: controller.messageText.value,
              initialValue: controller.messageText.value,
              onChange: (text) {
                controller.messageText.value = text;
              },
            )),
          ),
          Obx(() => IconButton(
            icon: Icon(
              Icons.send,
              color: controller.messageText.trim().isNotEmpty
                  ? AppColors.primary
                  : Colors.grey,
              size: 28,
            ),
            onPressed: controller.messageText.trim().isNotEmpty
                ? () {
              FocusManager.instance.primaryFocus?.unfocus();
              controller.handleSendPressed(
                types.PartialText(text: controller.messageText.value.trim()),
              );
              controller.messageText.value = '';
            }
                : null,
          )),

        ],
      ),
    );
  }

  Widget _buildVoiceIndicator() {
    return Obx(() {
      return TweenAnimationBuilder<double>(
        tween: Tween<double>(
          begin: 1.0,
          end: controller.isListening.value ? 1.3 : 1.0,
        ),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        builder: (context, scale, child) {
          return Transform.scale(
            scale: scale,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withOpacity(0.2),
              ),
              child: const Icon(
                Icons.mic,
                color: Colors.red,
                size: 28,
              ),
            ),
          );
        },
        onEnd: () {
          if (controller.isListening.value) {
            _buildVoiceIndicator();
          }
        },
      );
    });
  }
}
