import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/features/chatbot/presentation/controller/chatbot_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WelcomeMessageWidget extends StatelessWidget {
  const WelcomeMessageWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeHeader(),
          const SizedBox(height: 16),
          _buildInfoMessages(),
          const SizedBox(height: 16),
          _buildOptions(),
        ],
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Row(
      children: [
        Container(

          child: Image.asset(
            AppImageString.iChatBot,
            width: 50,
            height: 50,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Chào mừng bạn! Hôm nay tôi có thể giúp gì cho bạn?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoMessages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoItem(
          '📋 Bạn có thể hỏi tôi về quy trình cấp giấy chứng nhận, danh sách cơ sở sản xuất kinh doanh, thống kê và nhiều hơn nữa!',
        ),
        const SizedBox(height: 8),
        _buildInfoItem(
          '📝 Hãy nhập câu hỏi của bạn, ví dụ: "Làm thế nào để xin cấp giấy chứng nhận an toàn thực phẩm?"',
        ),
        const SizedBox(height: 8),
        _buildInfoItem(
          '🔍 Bạn cần tìm thông tin gì? Tôi có thể giúp bạn tra cứu nhanh chóng!',
        ),
        const SizedBox(height: 8),
        _buildInfoItem(
          '📑 Bạn cần tra cứu thông tin về giấy chứng nhận an toàn thực phẩm hay hỗ trợ gì khác:',
        ),
      ],
    );
  }

  Widget _buildInfoItem(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(text),
    );
  }

  Widget _buildOptions() {
    return Column(
      children: [
        _buildOptionButton('📑 Điều kiện cấp giấy chứng nhận ATTP?'),
        const SizedBox(height: 8),
        _buildOptionButton('📊 Danh sách cơ sở đã được cấp giấy?'),
        const SizedBox(height: 8),
        _buildOptionButton('📊 Thống kê cơ sở đủ điều kiện cấp GCN?'),
      ],
    );
  }

  Widget _buildOptionButton(String text) {
    return InkWell(
      onTap: () {
        Get.find<ChatBotController>().handleOptionSelected(text);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary),
          borderRadius: BorderRadius.circular(24),
        ),
        child: Text(
          text,
          style: TextStyle(color: AppColors.primary),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}