import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/features/chatbot/presentation/controller/chatbot_controller.dart';
import 'package:attp_2024/features/chatbot/presentation/widget/chat_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatBotPage extends GetView<ChatBotController> {
  const ChatBotPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWidget(
        title: "Chatbot",
        centerTitle: true,
      ),
      body: ChatViewWidget(),
    );
  }
}
