import 'dart:convert';
import 'dart:io';
import 'package:attp_2024/features/chatbot/model/request/chat_request.dart';
import 'package:attp_2024/features/chatbot/presentation/service/chat_service.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:uuid/uuid.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:url_launcher/url_launcher.dart';

class ChatBotController extends GetxController {
  final RxList<types.Message> messages = <types.Message>[].obs;
  final user = const types.User(
    id: '82091008-a484-4a89-ae75-a22bf8d6f3ac',
  );

  final botUser = const types.User(
    id: 'bot',
    firstName: 'AI Assistant',
    imageUrl: 'assets/images/chatbot.gif',
  );

  final ChatService _chatService = ChatService();
  final String _sessionId = const Uuid().v4();
  final stt.SpeechToText _speech = stt.SpeechToText();
  RxBool isListening = false.obs;
  RxString recognizedText = ''.obs;
  RxDouble decibels = 0.0.obs;
  final isTyping = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initSpeech();
    messages.value = [];
    //loadMessages();
    _addWelcomeMessage();
  }

  Future<void> _initSpeech() async {
    bool available = await _speech.initialize(
      onStatus: (status) {
        print('Speech status: $status');
        if (status == 'done' || status == 'notListening') {
          isListening.value = false;
        }
      },
      onError: (errorNotification) => print('Speech error: $errorNotification'),
    );
    print('Speech available: $available');
  }

  void addMessage(types.Message message) {
    print("Adding message: ${message.toJson()}");
    messages.insert(0, message);
    update();
  }

  Future<void> handleFileSelection(Function(types.Message) onMessageAdded) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
    );

    if (result != null && result.files.single.path != null) {
      final message = types.FileMessage(
        author: user,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        id: const Uuid().v4(),
        mimeType: lookupMimeType(result.files.single.path!),
        name: result.files.single.name,
        size: result.files.single.size,
        uri: result.files.single.path!,
      );

      onMessageAdded(message);
    }
  }

  Future<void> handleImageSelection(Function(types.Message) onMessageAdded) async {
    final result = await ImagePicker().pickImage(
      imageQuality: 70,

      maxWidth: 1440,
      source: ImageSource.gallery,
    );

    if (result != null) {
      final bytes = await result.readAsBytes();
      final image = await decodeImageFromList(bytes);

      final message = types.ImageMessage(
        author: user,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        height: image.height.toDouble(),
        id: const Uuid().v4(),
        name: result.name,
        size: bytes.length,
        uri: result.path,
        width: image.width.toDouble(),
      );

      onMessageAdded(message);
    }
  }

  Future<void> handleVoiceMessage(Function(types.Message) onMessageAdded) async {
    try {
      if (!_speech.isAvailable) {
        print('Speech recognition not available');
        Get.snackbar(
          'Thông báo',
          'Thiết bị của bạn không hỗ trợ nhận dạng giọng nói',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      if (!isListening.value) {
        isListening.value = true;
        try {
          await _speech.listen(
            localeId: 'vi_VN',
            onResult: (result) {
              recognizedText.value = result.recognizedWords;
              print("Recognized text: ${result.recognizedWords}");
              if (result.finalResult) {
                isListening.value = false;
                String text = result.recognizedWords;
                if (text.isNotEmpty) {
                  handleSendPressed(types.PartialText(text: text));
                  // Get.snackbar(
                  //   'Thành công',
                  //   'Đã nhận dạng: "$text"',
                  //   snackPosition: SnackPosition.BOTTOM,
                  //   backgroundColor: Colors.green.withOpacity(0.1),
                  // );
                }
              }
            },
            onSoundLevelChange: (level) {
              decibels.value = level * 100;
              print("Sound level: ${decibels.value}");
            },
            cancelOnError: true,
          );
        } catch (e) {
          print("Error during speech recognition: $e");
          isListening.value = false;
          Get.snackbar(
            'Lỗi',
            'Không thể bắt đầu nhận dạng giọng nói: $e',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        isListening.value = false;
        decibels.value = 0;
        await _speech.stop();
        print("Speech recognition stopped");
      }
    } catch (e) {
      print("General error: $e");
      isListening.value = false;
      decibels.value = 0;
      Get.snackbar(
        'Lỗi',
        'Đã xảy ra lỗi: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> handleMessageTap(types.Message message) async {
    if (message is types.TextMessage && message.metadata != null) {
      final url = message.metadata!['url'] as String?;
      if (url != null) {
        try {
          final uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri);
          }
        } catch (e) {
          print('Error launching URL: $e');
          Get.snackbar(
            'Lỗi',
            'Không thể mở liên kết',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      }
    }
  }

  Future<void> loadMessages() async {
    try {
      final response = await rootBundle.loadString('assets/messages.json');
      final loadedMessages = (jsonDecode(response) as List)
          .map((e) => types.Message.fromJson(e as Map<String, dynamic>))
          .toList();
      messages.value = loadedMessages;
    } catch (e) {
      print('Error loading messages: $e');
    }
  }

  var messageText = ''.obs;
  void handleSendPressed(types.PartialText message) async {
    if (message.text.trim().isEmpty) return;

    final textMessage = types.TextMessage(
      author: user,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      text: message.text,
    );

    addMessage(textMessage);

    try {
      final request = ChatRequest(
        message: message.text,
        sessionId: _sessionId,
      );

      final response = await _chatService.sendMessage(request);
      final baseurlApi =  dotenv.env['APICHAT'] ?? '';

      types.Message botMessage;
      
      switch (response.type) {
        case 'text':
          botMessage = types.TextMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            text: response.message,
          );
          break;
        
        case 'img':
          final fullUrl = '$baseurlApi${response.url}';
          botMessage = types.ImageMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            name: 'image',
            size: 0,
            uri: fullUrl,
            width: 300,
            height: 200,
            metadata: {
              'downloadUrl': fullUrl,
              'isDownloadable': true,
            },
          );
          break;
        
        case 'pdf':
        case 'excel':
          final fullUrl = '$baseurlApi${response.url}';
          final linkText = response.type == 'pdf' ? 'Xem file' : 'Tải tệp';
          botMessage = types.TextMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            text: linkText,
            metadata: {
              'url': fullUrl,
              'type': response.type,
            },
          );
          break;
        
        default:
          botMessage = types.TextMessage(
            author: botUser,
            createdAt: DateTime.now().millisecondsSinceEpoch,
            id: const Uuid().v4(),
            text: response.message,
          );
      }

      addMessage(botMessage);
    } catch (e) {
      print('Error sending message: $e');
    }

    messageText.value = '';
  }


  void _addWelcomeMessage() {
    final welcomeMessage = types.CustomMessage(
      author: botUser,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      metadata: {
        'type': 'welcome_message',
      },
    );
    addMessage(welcomeMessage);
  }

  void handleOptionSelected(String option) {

    final userMessage = types.TextMessage(
      author: user,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      text: option,
    );
    
    addMessage(userMessage);
    
    final request = ChatRequest(
      message: option,
      sessionId: _sessionId,
    );
    
    _chatService.sendMessage(request).then((response) {
      final botMessage = types.TextMessage(
        author: botUser,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        id: const Uuid().v4(),
        text: response.message,
      );
      
      addMessage(botMessage);
    }).catchError((error) {
      print('Error sending message: $error');
    });
  }
}
