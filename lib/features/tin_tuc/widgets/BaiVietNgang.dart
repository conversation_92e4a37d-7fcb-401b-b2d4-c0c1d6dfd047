// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/tin_tuc/model/NewsModel.dart';
import 'package:attp_2024/features/tin_tuc/presentation/controller/tinTuc_controller.dart';
import 'package:attp_2024/features/tin_tuc/presentation/page/tinTuc_detail.dart';

class NewListHorizontalScrollViewPage extends GetView<TinTucController> {
  final List<NewsItem> hotFilteredNews;

  const NewListHorizontalScrollViewPage(
      {super.key, required this.hotFilteredNews});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 250, // Chiều cao của danh sách cuộn ngang
      child: ListView.builder(
        padding: EdgeInsets.only(left: 3.w),
        scrollDirection: Axis.horizontal,
        itemCount: hotFilteredNews.length,
        itemBuilder: (context, index) {
          final news = hotFilteredNews[index];

          return GestureDetector(
            onTap: () {
              Get.back();
              Get.put(TinTucController());
              Get.to(() => TinTucDetailPage(news: news));
            },
            child: Container(
              width: 270, // Đặt chiều rộng cố định cho mỗi thẻ tin tức
              margin: const EdgeInsets.only(right: 13),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 240,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        // ignore: deprecated_member_use
                        color: Colors.black.withOpacity(0.1),
                        width: 0.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          // ignore: deprecated_member_use
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Hình ảnh
                          ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  8), // Bo tròn góc cho ảnh
                              child: CustomCachedImage(
                                imageUrl:
                                    '${controller.userAccessModel?.siteURL}${news.HinhAnh}',
                                defaultImage: AppImageString.imageNotFount,
                                width: double.infinity,
                                height: 15.h,
                                fit: BoxFit.cover,
                              )),
                          const SizedBox(
                              height: 8), // Khoảng cách giữa ảnh và chữ
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              spacing: 3,
                              children: [
                                Text(
                                  news.TieuDe,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                TextWidget(
                                  text:
                                      '${news.TenLoaiBaiViet} • ${news.NgayTao}',
                                  size: AppDimens.textSize12,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.gray2,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
