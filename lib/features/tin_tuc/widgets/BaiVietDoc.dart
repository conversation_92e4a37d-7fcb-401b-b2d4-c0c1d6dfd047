// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/features/tin_tuc/presentation/controller/tinTuc_controller.dart';
import 'package:attp_2024/features/tin_tuc/widgets/VerticalNewsItem.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class DanhSachBaiViet extends GetView<TinTucController> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Obx(() {
        // Lọc danh sách tin tức theo danh mục nếu được chọn
        final filteredNews = controller.selectedTabIndex.value ==
                '00000000-0000-0000-0000-000000000000'
            ? controller.allNewsItems // Hiển thị tất cả item
            : controller.allNewsItems
                .where((news) =>
                    news.LoaiBaiVietID == controller.selectedTabIndex.value)
                .toList();

        return SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Padding(
            padding: EdgeInsets.only(
              bottom: 12.h,
            ),
            child: Column(
              spacing: 2.h,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: filteredNews.map((newsItem) {
                    return buildVerticalNewsItem(newsItem, context,
                        '${controller.userAccessModel?.siteURL}');
                  }).toList(),
                ),
                GestureDetector(
                  onTap: () => controller.fetchNewsByID(
                      controller.selectedTabIndex.value,
                      controller.pageIndex.value),
                  child: controller.hetDuLieuRoi.value
                      ? const SizedBox.shrink()
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                              const Icon(
                                Icons.keyboard_double_arrow_down_sharp,
                                size: 19,
                              ),
                              TextWidget(
                                text:
                                    'Xem thêm ${controller.baiVietConLai.value} bài viết',
                                color: AppColors.gray3,
                                textAlign: TextAlign.start,
                                size: AppDimens.textSize14,
                                fontWeight: FontWeight.w600,
                              ),
                            ]),
                )
              ],
            ),
          ),
        );
      }),
    );
  }
}
