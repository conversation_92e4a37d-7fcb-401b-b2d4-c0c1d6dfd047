import 'package:attp_2024/core/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/ui/widgets/searchPage/search_page.dart';
import 'package:attp_2024/features/tin_tuc/model/NewsModel.dart';
import 'package:attp_2024/features/tin_tuc/presentation/controller/tinTuc_controller.dart';
import 'package:attp_2024/features/tin_tuc/widgets/DanhSachTab.dart';
import 'package:attp_2024/features/tin_tuc/widgets/BaiVietNgang.dart';
import 'package:attp_2024/features/tin_tuc/widgets/BaiVietDoc.dart';
import 'package:attp_2024/features/tin_tuc/widgets/VerticalNewsItem.dart';

class TinTucPage extends GetView<TinTucController> {
  // final TinTucController controller = Get.put(TinTucController());

  const TinTucPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          // controller.selectedIndex == 1
          // ? null
          // :
          AppBarWidget(
        title: "     Tin Tức",

        // automaticallyImplyLeading: true,
        hiddenLeading: true,
        // hiddenLeading: controller.selectedIndex == 1 ? true : false,
        // centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.search,
              size: 30,
              color: AppColors.white,
            ),
            onPressed: () {
              Get.to(() => SearchPage(
                  data: controller.allNewsItems,
                  searchByField: (news) => (news as NewsItem).TieuDe,
                  searchedItemBuilder: (item) {
                    final news = item as NewsItem;
                    return buildVerticalNewsItem(news, context,
                        '${controller.userAccessModel?.siteURL}');
                  }));
            },
          ),
        ],
      ),
      body: Loading.LoadingFullScreen(
        isLoading: controller.isLoading,
        body: SafeArea(
          child: _buildBody(context),
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      return ListView(
        children: [
          const Gap(10),
          NewsTabs(),
          controller.hotFilteredNews.isNotEmpty
              ? const Gap(20)
              : const SizedBox.shrink(),
          controller.hotFilteredNews.isNotEmpty
              ? Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: Row(children: [
                    const TextWidget(
                      text: "Tin nổi bật",
                      size: AppDimens.textSize18,
                      fontWeight: FontWeight.w700,
                      textAlign: TextAlign.left,
                    ),
                    Expanded(
                      child: Divider(
                        color: Colors.blueGrey[100],
                        thickness: 1.0,
                        indent: 16.0,
                        endIndent: 16.0,
                      ),
                    ),
                  ]),
                )
              : const SizedBox.shrink(),
          controller.hotFilteredNews.isNotEmpty
              ? const Gap(20)
              : const SizedBox.shrink(),
          controller.hotFilteredNews.isNotEmpty
              ? NewListHorizontalScrollViewPage(
                  hotFilteredNews: controller.hotFilteredNews,
                )
              : const SizedBox.shrink(),
          const Gap(15),
          Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: Row(children: [
              const TextWidget(
                text: "Tin tức mới nhất",
                size: AppDimens.textSize18,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.left,
              ),
              Expanded(
                child: Divider(
                  color: Colors.blueGrey[100],
                  thickness: 1.0,
                  indent: 16.0,
                  endIndent: 16.0,
                ),
              ),
            ]),
          ),
          const Gap(20),
          DanhSachBaiViet(),
        ],
      );
    });
  }
}
