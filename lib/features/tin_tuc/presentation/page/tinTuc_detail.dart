// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
import 'package:attp_2024/features/main/presentation/pages/main_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/share/share.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/convert_text.dart';
import 'package:attp_2024/core/utils/screen.dart';
import 'package:attp_2024/features/tin_tuc/model/NewsModel.dart';
import 'package:attp_2024/features/tin_tuc/presentation/controller/tinTuc_controller.dart';
import 'package:attp_2024/features/tin_tuc/widgets/BaiVietNgang.dart';

import '../../../../core/ui/widgets/customCachedImage/customCachedImage.dart';

class TinTucDetailPage extends GetView<TinTucController> {
  final NewsItem news;

  const TinTucDetailPage({
    super.key,
    required this.news,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final relatedNews = controller.allNewsItems
          .where((item) => item.LoaiBaiVietID == news.LoaiBaiVietID)
          .take(10)
          .toList();

      return Scaffold(
        appBar: AppBarWidget(
          title: truncateText(
            news.TieuDe,
            const TextStyle(fontSize: 16),
            screenWidthPercentage(context, 65),
          ),
          titleSize: 18,
          actions: [
            ShareWidget(
                text: convertHtmlToString(news.NoiDungTomTat,
                    "Nội dung tóm tắt không thể hiển thị ..."))
          ],
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: AppColors.white,
            ),
            onPressed: () => Get.back(),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tiêu đề tin tức
                TextWidget(
                  text: news.TieuDe,
                  size: AppDimens.textSize24,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 16),

                // Ngày và danh mục tin tức
                TextWidget(
                  text: '${news.TenLoaiBaiViet}  •  ${news.NgayTao}',
                  size: AppDimens.textSize14,
                  color: AppColors.gray2,
                  fontWeight: FontWeight.w500,
                ),
                const SizedBox(height: 16),
                Obx(
                  () => CustomCachedImage(
                    imageUrl: '${controller.url.value}/${news.HinhAnh}',
                    defaultImage: AppImageString.imageNotFount,
                    width: double.infinity,
                    height: 25.h,
                    fit: BoxFit.cover,
                  ),
                ),

                const SizedBox(height: 16),

                // Nội dung tin tức
                HtmlWidget(news.NoiDung),
                const SizedBox(height: 16),

                // Người tạo tin tức
                Align(
                  alignment: Alignment.centerRight,
                  child: TextWidget(
                    text: news.NguoiTao,
                    size: AppDimens.textSize12,
                    color: AppColors.gray3,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.right,
                  ),
                ),
                const SizedBox(height: 30),

                // Tiêu đề "Tin liên quan"
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const TextWidget(
                      text: "Tin liên quan",
                      size: AppDimens.textSize18,
                      fontWeight: FontWeight.w700,
                      color: AppColors.red2,
                    ),
                    Expanded(
                        child: Divider(
                      color: AppColors.gray2,
                      thickness: 1,
                      endIndent: 10,
                      indent: 10,
                    )),
                    InkWell(
                      onTap: () async {
                        // Get.off(() => const MainPage());
                        Get.back();
                        Get.find<MainController>().onChangeItemBottomBar(1);
                        controller.selectedTabIndex.value = news.LoaiBaiVietID;
                        controller.handleChangeTab();
                      },
                      child: Row(
                        children: [
                          TextWidget(
                            text: "Xem thêm",
                            color: AppColors.primary,
                          ),
                          Icon(
                            Icons.arrow_forward_ios_rounded,
                            size: AppDimens.textSize16,
                            color: AppColors.primary,
                          )
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Danh sách tin liên quan
                relatedNews.isEmpty
                    ? const TextWidget(
                        text: "Không có tin liên quan.",
                        size: AppDimens.textSize14,
                        color: AppColors.gray2,
                      )
                    : NewListHorizontalScrollViewPage(
                        hotFilteredNews: relatedNews,
                      ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
