import 'package:get/get.dart';
import 'package:attp_2024/features/expanded/Ca<PERSON>han/developer/presentation/controller/developer_controller.dart';
import 'package:attp_2024/features/expanded/CaNhan/editProfile/presentation/controller/editProfile_controller.dart';
import 'package:attp_2024/features/nav/profile/presentation/controllers/profile_controller.dart';

class DeveloperBinding extends Bindings {
  @override
  void dependencies() {
    //cách 1 : khởi tạo trong lần đầu đượ<PERSON> gọ<PERSON>
    // sử dụng tối ưu hiệu năng không pải load api nhiều lần
    Get.lazyPut(() => DeveloperController());

    // cách 2: tạo đối tượng ngay lập tưc bắt kể là dùng hay hông dùng
    // dùng trong trường hợp cần sử dụng ngay controller
    // Get.put(ProfileController());
  }
}
