import 'dart:io';
import 'dart:typed_data';
import 'package:attp_2024/core/ui/widgets/CustomDatePicker/custom_date_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

import 'package:attp_2024/core/ui/widgets/appbar/app_bar_widget.dart';
import 'package:attp_2024/core/ui/widgets/custom_combo/combo.dart';
import 'package:attp_2024/core/ui/widgets/input/custom_radio.dart';
import 'package:attp_2024/core/ui/widgets/load/loading.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/core/utils/validator.dart';
import 'package:attp_2024/features/expanded/CaNhan/editProfile/presentation/controller/editProfile_controller.dart';
import '../../../../../../core/configs/dimens/app_dimens.dart';
import '../../../../../../core/ui/widgets/button/button_widget.dart';
import '../../../../../../core/ui/widgets/customCachedImage/customCachedImage.dart';
import '../../../../../../core/ui/widgets/custom_textfield/widgets/custom_textfield.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

class EditProfilePage extends GetView<EditProfileController> {
  const EditProfilePage({super.key});

  // @override
  // Widget build(BuildContext context) {
  //   return Loading.LoadingFullScreen(
  //     isLoading: controller.isLoadingUpdate,
  //     body: Scaffold(
  //       backgroundColor: AppColors.white,
  //       resizeToAvoidBottomInset: true,
  //       body: Stack(
  //         children: [
  //           Positioned(
  //             top: 0,
  //             left: 0,
  //             right: 0,
  //             child: _buildHeader(),
  //           ),
  //           Positioned(
  //             top: 25.w,
  //             left: 0,
  //             right: 0,
  //             child: _buildBody(context),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: "Cập nhật thông tin tài khoản",
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 6.0),
            child: TextButton(
              onPressed: () {
                if (controller.name.value.isEmpty ||
                    !Validators.validPhone(controller.phone.value) ||
                    !Validators.validateEmail(controller.email.value)) {
                  return;
                } else {
                  // controller.updateUserProfile(
                  //     userID: controller.userID.value);
                  // controller.updateUserAvatar(controller.avatarImage.value);
                  controller.updateUserInformation();
                }
              },
              child: const Text(
                "Lưu",
                style: TextStyle(
                    color: AppColors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
      body: Loading.LoadingFullScreen(
        isLoading: controller.isLoadingUpdate,
        body: _buildBody(context),
      ),
    );
  }

  Widget _buildHeader() {
    return Stack(
      children: [
        Container(
          width: 100.w,
          height: 12.h,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(20),
            ),
            image: DecorationImage(
              image: AssetImage('assets/images/ok.png'),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 3.w,
          left: 1.w,
          right: 5.w,
          bottom: 0.w,
          child: AppBarWidget(
            title: "Sửa thông tin cá nhân",
            backgroundColor: Colors.transparent,
            titleSize: 18,
            actions: [
              Padding(
                padding: const EdgeInsets.only(right: 6.0),
                child: TextButton(
                  onPressed: () {
                    if (controller.name.value.isEmpty ||
                        !Validators.validPhone(controller.phone.value) ||
                        !Validators.validateEmail(controller.email.value)) {
                      return;
                    } else {
                      // controller.updateUserProfile(
                      //     userID: controller.userID.value);
                      // controller.updateUserAvatar(controller.avatarImage.value);
                      controller.updateUserInformation();
                    }
                  },
                  child: const Text(
                    "Lưu",
                    style: TextStyle(
                        color: AppColors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // ignore: non_constant_identifier_names
  Widget _buildBody(BuildContext context) {
    print(controller.avatarPath.value);
    return SingleChildScrollView(
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 5.0),
              Stack(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.boderCircleAvatar,
                        width: 1,
                      ),
                    ),
                    child: Obx(
                      () => CircleAvatar(
                        radius: 50,
                        backgroundImage: controller
                                .avatarPathTemp.value.isNotEmpty
                            ? FileImage(File(controller.avatarPathTemp.value))
                                as ImageProvider
                            : controller.avatarPath.value.isNotEmpty
                                ? NetworkImage(controller.avatarPath.value)
                                    as ImageProvider
                                : const AssetImage(
                                    AppImageString.defaultProfile),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () => _showImageSourceModal(context),
                      child: CircleAvatar(
                        backgroundColor: Colors.black.withOpacity(0.5),
                        radius: 13,
                        child: const Icon(
                          CupertinoIcons.camera_fill,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Obx(
                () => SizedBox(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFieldWidget(
                      prefixIcon: Icon(
                        Icons.account_circle_outlined,
                        color: AppColors.primary,
                      ),
                      title: 'Họ và tên',
                      isRequired: true,
                      placeholder: 'Nhập họ và tên',
                      setValue: controller.name.value,
                      initialValue: '',
                      errorWidget: controller.name.value.isEmpty
                          ? 'Họ và tên không được để trống !'
                          : '',
                      onChange: (e) {
                        controller.name.value = e;
                      },
                    ),
                  ],
                )),
              ),
              SizedBox(
                  width: double.infinity,
                  child: Obx(
                    () => TextFieldWidget(
                      prefixIcon: Icon(Icons.phone_android_outlined,
                          color: AppColors.primary),
                      title: 'Số điện thoại',
                      placeholder: 'Nhập số điện thoại',
                      errorWidget: Validators.validPhone(controller.phone.value)
                          ? ""
                          : "Số điện thoại không hợp lệ !",
                      initialValue: '',
                      setValue: controller.phone.value,
                      onChange: (e) {
                        controller.phone.value = e;
                      },
                    ),
                  )),

              SizedBox(
                  width: double.infinity,
                  child: Obx(() => TextFieldWidget(
                        prefixIcon: Icon(Icons.location_on_outlined,
                            color: AppColors.primary),
                        title: 'Địa chỉ',
                        placeholder: 'Nhập địa chỉ',
                        initialValue: '',
                        setValue: controller.location.value,
                        onChange: (e) {
                          controller.location.value = e;
                        },
                      ))),
              SizedBox(
                  width: double.infinity,
                  child: Obx(() => TextFieldWidget(
                        prefixIcon: Icon(Icons.alternate_email_outlined,
                            color: AppColors.primary),
                        isRequired: false,
                        title: 'Email',
                        placeholder: 'Nhập Email',
                        errorWidget:
                            Validators.validateEmail(controller.email.value)
                                ? ''
                                : "Email không hợp lệ !",
                        initialValue: '',
                        setValue: controller.email.value,
                        onChange: (e) {
                          controller.email.value = e;
                        },
                      ))),
              Row(
                children: [
                  Obx(() => Expanded(
                        child: CustomCombobox(
                          prefixIcon: controller.numGender.value == 1
                              ? Icon(Icons.male, color: AppColors.primary)
                              : controller.numGender.value == 2
                                  ? Icon(Icons.female, color: AppColors.primary)
                                  : Icon(Icons.account_circle_outlined,
                                      color: AppColors.primary),
                          defaultSelectedItem: controller.gender.value,
                          dropDownList: controller.listGenders.toList(),
                          onChange: (e) {
                            final String? str_gender = e?.display.toString();
                            controller.gender.value.display = str_gender;
                            str_gender == 'Nam'
                                ? controller.numGender.value = 1
                                : str_gender == 'Nữ'
                                    ? controller.numGender.value = 2
                                    : controller.numGender.value = 0;
                          },
                          title: 'Giới tính',
                        ),
                      )),
                ],
              ),
              SizedBox(
                  width: double.infinity,
                  child: Obx(
                    () => CustomDatePicker(
                      prefixIcon:
                          Icon(Icons.cake_outlined, color: AppColors.primary),
                      isMaximumDate: true,
                      isRequired: false,
                      title: 'Ngày sinh',
                      placeholder: 'dd-MM-yyyy',
                      date: controller.birthday.value,
                      // Ngày mặc định
                      setTime: controller.birthday.value,
                      onChange: (DateTime? newDate) {
                        print('Ngày đã chọn: $newDate');
                        controller.birthday.value = newDate!;
                      },
                    ),
                  )),

              // SizedBox(
              //   height: 45,
              //   child: ButtonWidget(
              //     ontap: () {
              //       if (controller.name.value.isEmpty) {
              //         // Hiện thông báo hoặc làm gì đó khi tên rỗng
              //         return;
              //       } else {
              //         // Cập nhật thông tin người dùng và ảnh đại diện
              //         controller.updateUserProfile(
              //             userID: controller.userID.value);
              //         controller.updateUserAvatar(controller.avatarImage.value);
              //       }
              //     },
              //     text: "Lưu thông tin",
              //     backgroundColor: AppColors.submitButtonColor,
              //     borderRadius: 5,
              //   ),
              // )
              // Các thành phần khác trong Column
              const SizedBox(
                height: 20,
              ),
              // _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(10),
      color: AppColors.grey,
      child: const Center(
        child: Text(
          'Thông tin phần mềm: Quản lý an toàn thực phẩm',
          style: TextStyle(color: AppColors.white),
        ),
      ),
    );
  }

  void _showImageSourceModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          height: 190,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Chọn ảnh từ: ',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              ListTile(
                leading: Icon(Icons.photo_library, color: AppColors.green2),
                title: const Text('Thư viện'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: Icon(Icons.camera_alt, color: AppColors.green2),
                title: const Text('Máy ảnh'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _pickImage(ImageSource source) async {
    controller.isLoading.value = true;
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: source);

    if (image != null) {
      try {
        controller.avatarPath.value = image.path;
        controller.avatarPathTemp.value = image.path;
        controller.avatarImage.value = image;
        print('Đường dẫn ảnh: ${image.path}');
      } catch (e) {
        print('Error uploading image: $e');
      }
    } else {
      controller.isLoading.value = false;
      // User cancelled image picking
    }
    controller.isLoading.value = false;
  }
}
