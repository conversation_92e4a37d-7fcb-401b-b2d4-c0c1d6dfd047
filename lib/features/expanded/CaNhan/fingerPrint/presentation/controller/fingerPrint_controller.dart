// ignore: file_names
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/database/device_data.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/device_response.dart';
import 'package:attp_2024/core/data/dto/response/device_response_model.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/services/fingerprint_use_case.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/utils/info_device.dart';

class FingerprintController extends GetxController {
  var switchActive = false.obs;

  RxBool isLoading = false.obs;

  DeviceResponse? infoDevice;
  var supportedBiometric = "".obs;
  List<DeviceResponseModel> listDevice = [];

  DeviceData deviceData = DeviceData();

  UserAccessModel? user;

  final LocalAuthentication auth = LocalAuthentication();

  @override
  onInit() async {
    super.onInit();
    await initial();
    await getAvailableBiometricTypes();
  }

  Future<void> initial() async {
    isLoading.value = true;

    infoDevice = await InfoDevice.getDeviceData();

    user = await UserUseCase.getUser();

    switchActive.value = await FingerprintUseCase.getSateFingerprintAuth();

    var deviceData = DeviceData();

    var result = await deviceData.getListDevice(
        request: ProcRequestModel(fields: [
      FieldModel(name: "UserID", type: "guid", value: user?.userID ?? "")
    ]));

    if (result.status == Status.success) {
      listDevice = result.data ?? [];
      listDevice.sort((a, b) {
        if (a.thietBiCode == infoDevice?.thietBiCode) return -1;
        if (b.thietBiCode == infoDevice?.thietBiCode) return 1;
        return 0;
      });
      update(["bodyID"]);
    }
    isLoading.value = false;
  }

  Future<void> getAvailableBiometricTypes() async {
    try {
      List<BiometricType> availableBiometrics =
          await auth.getAvailableBiometrics();
      if (availableBiometrics.contains(BiometricType.face)) {
        print("Face ID available.");
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        print("Fingerprint available.");
      } else {
        print("No biometrics enrolled.");
      }
    } catch (e) {
      print("Error fetching biometric types: $e");
    }
  }

  Future<void> updateStateDevice(
      {required bool currentSate,
      required String userId,
      required String thietBiCode}) async {
    await deviceData.updateStateDevice(
        request: ProcRequestModel(fields: [
      FieldModel(name: "ThietBiCode", type: "String", value: thietBiCode),
      FieldModel(
          name: "TenThietBi",
          type: "String",
          value: infoDevice?.tenThietBi ?? ""),
      FieldModel(
          name: "HangSX", type: "String", value: infoDevice?.hangSX ?? ""),
      FieldModel(
          name: "Platform", type: "String", value: infoDevice?.platform ?? ""),
      FieldModel(
          name: "Version", type: "String", value: infoDevice?.version ?? ""),
      FieldModel(
          name: "LoaiThietBi",
          type: "String",
          value: infoDevice?.loaiThietBi ?? ""),
      FieldModel(name: "UserID", type: "guid", value: userId),
      FieldModel(name: "NgungSD", type: "boolean", value: !currentSate),
    ]));
  }

  Future<void> handleChangeSwich({required bool currentState}) async {
    if (currentState) {
      await FingerprintUseCase.turnOnFingerprintAuth();
    } else {
      await FingerprintUseCase.turnOffFingerprintAuth();
    }
    switchActive.value = await FingerprintUseCase.getSateFingerprintAuth();
  }
}
