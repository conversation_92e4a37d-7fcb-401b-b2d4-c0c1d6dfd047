import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/data/api/services/api/api_service.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/utils/validator.dart';

class ChangePasswordController extends GetxController {
  var secret = ''.obs;
  var username = ''.obs;
  var password = ''.obs;
  var newPassword = ''.obs;
  final isLoading = false.obs;
  final errPassword = ''.obs;
  final errNewPassword = ''.obs;
  final errRePassword = ''.obs;

  final rePassword = ''.obs;
  final _apiService = APIService();

  final userID = ''.obs;

  Future<void> onInit() async {
    secret.value = await _getpassword();
    username.value = await _getusername();
  }

  Future<String> _getusername() async {
    final userId = await UserUseCase.getUser();
    return userId!.userName;
  }

  Future<String> _getpassword() async {
    final userId = await UserUseCase.getUser();
    return userId!.password;
  }

  bool checkPassword() {
    if (password.value.isEmpty) {
      errPassword.value = 'Chưa nhập mật khẩu';
      return false;
    }
    if (newPassword.value != rePassword.value) {
      errRePassword.value = 'Mật khẩu không khớp';
      errNewPassword.value = 'Mật khẩu không khớp';
      return false;
    } else {
      errRePassword.value = '';
      errNewPassword.value = '';
      if (Validators.validPassword(errNewPassword.value)) {
        errRePassword.value = 'Mật khẩu mới không đúng định dạng';
        return false;
      } else {
        errRePassword.value = '';
        return true;
      }
    }
  }

  Future<void> doiMatKhau(BuildContext context) async {
    isLoading.value = true;
    log(username.value, name: 'akr');
    final Map<String, dynamic> body = {
      "TenDangNhap": username.value,
      "MatKhau": password.value,
      "MatKhauMoi": newPassword.value
    };
    try {
      final response = await _apiService.callAPI("/doimatkhau", body);
      if (response["success"] == true) {
        log('Đổi mật khẩu thành công', name: 'akr');
        SnackbarUtil.showSuccess( "Đổi mật khẩu thành công",
            alignment: "bottom");
        Get.back();
      } else {
        SnackbarUtil.showError( "Đổi mật khẩu thất bại",
            alignment: "bottom");
      }
    } catch (e) {
      log("Error: $e", name: 'akr');
      SnackbarUtil.showError( "Đã xảy ra lỗi khi đổi mật khẩu",
          alignment: "bottom");
    } finally {
      isLoading.value = false;
    }
  }
}
