import 'package:attp_2024/core/data/api/services/proc/proc_service.dart';
import 'package:attp_2024/core/data/dto/response/permission_response.dart';
import 'package:attp_2024/core/data/dto/response/profile_user_model.dart';
import 'package:attp_2024/core/services/permission_use_case.dart';
import 'package:attp_2024/features/nav/home/<USER>/thamdinh_model.dart';
import 'package:attp_2024/features/nav/home/<USER>/home_serivces.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';
import 'package:attp_2024/features/tin_tuc/model/NewsModel.dart';
import 'package:attp_2024/features/tin_tuc/services/new_service.dart';
import 'package:logger/logger.dart';

class HomeController extends GetxController {
  // UserAccessModel? userAccessModel;
  final userID = ''.obs;
  final _procService = ProcService();
  RxString avatarPathOld = ''.obs;
  RxString name = ''.obs;
  @override
  void onInit() async {
    super.onInit();
    await loadInfoProfile();
    userID.value = await getUserID();
    await Future.wait([
      checkPermission(),
      fetchDataCardGeneric(serviceHome.fetchDataCard, cardType: 1),
      fetchDataCardGeneric(serviceHome.fetchDataCardTab2, cardType: 2),
      fetchNews(10),
      fetchDanhSachThamDinh("CapGCN", danhSachThamDinh),
      fetchDanhSachThamDinh("KyGCK", danhSachThamDinhTab2),
      fetchChar1(currentFilterChar_tab1.value),
      fetchChar2(),
      fetchUserProfile(userID: userID.value),
    ]);
    update(["bodyID"]);
  }

  Future<String> getUserID() async {
    final userId = await UserUseCase.getUser();
    return userId!.userID;
  }

  Future<void> fetchUserProfile({required String userID}) async {
    final List<Map<String, dynamic>> body = [
      {"name": "UserID", "type": "Guid", "value": userID},
    ];
    try {
      final response =
          await _procService.callProc("Proc_Mobile_GetUserInfo_byDB", body);
      if (response.isNotEmpty) {
        final userProfile = response
            .map((json) => ProfileUserModel(
                  diaChi: json['DiaChi'] ?? '',
                  tenDangNhap: json['TenDangNhap'] ?? '',
                  hoTen: json['HoTen'] ?? '',
                  gioiTinh: json['GioiTinh'] ?? '',
                  diDong: json['DiDong'] ?? '',
                  email: json['Email'] ?? '',
                  ngaySinh: json['NgaySinh'] ?? '',
                  hinhDaiDien: json['HinhDaiDien'] ?? '',
                  donViCode: json['DonViCode'] ?? '',
                  tenDonVi: json['TenDonVi'] ?? '',
                  donViID: json['DonViID'] ?? '',
                  tinhID: json['TinhID'] ?? '',
                  huyenID: json['HuyenID'] ?? '',
                  xaID: json['XaID'] ?? '',
                  thonID: json['ThonID'] ?? '',
                  tenTinh: json['TenTinh'] ?? '',
                  tenHuyen: json['TenHuyen'] ?? '',
                  tenXa: json['TenXa'] ?? '',
                  tenThon: json['TenThon'] ?? '',
                  userGroupID: json['UserGroupID'] ?? '',
                  userGroupCode: json['UserGroupCode'] ?? '',
                ))
            .first;
        name.value = userProfile.hoTen;
        avatarPathOld.value = userProfile.hinhDaiDien;
      }
    } catch (e) {
    } finally {}
  }

  var userAccessModel = UserAccessModel.defaultAccount.obs;

  final Logger logger = Logger();

  // Observables cho thống kê của Tab 1
  final dncGCN = '0'.obs;
  final paATTP = "0".obs;
  final csTD = "0".obs;
  final viphamATTP = "0".obs;
  final date = "".obs;
  final RxString currentFilteCh_tb1 = 'Tuần này'.obs;
  final RxString currentFilteCh_tb2 = 'Tuần này'.obs;
  final RxString currentFilterChar_tab1 = 'Tháng 1'.obs;
  final RxString currentFilterChar_tab2 = 'Tháng 1'.obs;
  final List<Map<String, String>> filterOptions = [
    {"label": "Hôm nay", "value": "DAY"},
    {"label": "Tuần này", "value": "WEEK"},
    {"label": "Tháng này", "value": "MONTH"},
    {"label": "Quý này", "value": "QUARTER"},
    {"label": "Năm này", "value": "YEAR"},
  ];

  final List<Map<String, String>> filterOptions2 = [
    {"label": "Tháng 1", "value": "JANUARY"},
    {"label": "Tháng 2", "value": "FEBRUARY"},
    {"label": "Tháng 3", "value": "MARCH"},
    {"label": "Tháng 4", "value": "APRIL"},
    {"label": "Tháng 5", "value": "MAY"},
    {"label": "Tháng 6", "value": "JUNE"},
    {"label": "Tháng 7", "value": "JULY"},
    {"label": "Tháng 8", "value": "AUGUST"},
    {"label": "Tháng 9", "value": "SEPTEMBER"},
    {"label": "Tháng 10", "value": "OCTOBER"},
    {"label": "Tháng 11", "value": "NOVEMBER"},
    {"label": "Tháng 12", "value": "DECEMBER"},
    {"label": "Năm", "value": "YEAR"},
    {"label": "Quý I", "value": "QUARTER_1"},
    {"label": "Quý II", "value": "QUARTER_2"},
    {"label": "Quý III", "value": "QUARTER_3"},
    {"label": "Quý IV", "value": "QUARTER_4"},
    {"label": "6 tháng đầu năm", "value": "FIRST_HALF"},
    {"label": "6 tháng cuối năm", "value": "SECOND_HALF"},
  ];

  // Observables cho thống kê của Tab 2
  final dncGCN2 = '0'.obs;
  final paATTP2 = "0".obs;
  final csTD2 = "0".obs;
  final viphamATTP2 = "0".obs;
  final RxList<ThamDinhModel> danhSachThamDinh = <ThamDinhModel>[].obs;
  final RxList<ThamDinhModel> danhSachThamDinhTab2 = <ThamDinhModel>[].obs;
  final RxList<NewsItem> newsList = <NewsItem>[].obs;

  final RxList<int> certificationValues = [0, 0, 0, 0].obs;
  final RxList<int> certificationValues1 = [0, 0].obs;

  final serviceHome = HomeService();

  Future<void> loadInfoProfile() async {
    try {
      final userData = await UserUseCase.getUser();
      userAccessModel.value =
          userData!; // ✅ Giữ dữ liệu ngay cả khi quay lại trang
      logger.i('Tải thông tin người dùng thành công: ${userData?.tenDangNhap}');
    } catch (e) {
      logger.e('Lỗi khi tải thông tin người dùng', error: e);
    }
  }

  Future<void> fetchDanhSachThamDinh(
      String type, RxList<ThamDinhModel> targetList) async {
    logger.t(date.value);
    try {
      final thamDinhList =
          await serviceHome.fetchDataDanhSachThamDinhATTP(type, date.value);
      targetList.assignAll(thamDinhList.take(4).toList());
      logger.i(
          'Danh sách thẩm định ($type) đã được tải: ${targetList.map((e) => e.soBienBan).toList()}');
    } catch (e, stackTrace) {
      logger.e('Lỗi khi tải danh sách thẩm định',
          error: e, stackTrace: stackTrace);
    }
  }

  Future<void> fetchChar1(String selected) async {
    print(selected);
    try {
      var data = await serviceHome.fetchDataCharGCN(selected);
      if (data.isNotEmpty) {
        certificationValues[0] = data[0]["CapMoi"] ?? 0;
        certificationValues[1] = data[0]["CapLai"] ?? 0;
        certificationValues[2] = data[0]["ThuHoi"] ?? 0;
        certificationValues[3] = data[0]["HetHan"] ?? 0;
      } else {
        certificationValues.value = [0, 0, 0, 0]; // Reset if no data
      }
    } catch (e, stackTrace) {
      print('Error: $e');
    }
  }

  Future<void> fetchChar2() async {
    try {
      var data =
          await serviceHome.fetchDataCharGCK(currentFilterChar_tab2.value);
      if (data.isNotEmpty) {
        certificationValues1[0] = data[0]["CapMoi"] ?? 0;
        certificationValues1[1] = data[0]["HetHan"] ?? 0;
      } else {
        certificationValues1.value = [0, 0];
      }
    } catch (e, stackTrace) {
      print('Error: $e');
    }
  }

  /// Lấy dữ liệu thống kê (generic cho cả tab 1 và tab 2)
  Future<void> fetchDataCardGeneric(
      Future<List<dynamic>> Function() fetchFunction,
      {required int cardType}) async {
    try {
      final List<dynamic> data = await fetchFunction();

      // Xác định mapping theo cardType
      final Map<String, RxString> mapping = cardType == 1
          ? {
              'SoLuongCXKyCamKet': dncGCN,
              'SoLuongKiemTraThamDinh': csTD,
              'SoLuongKhieuNai': paATTP,
              'SoLuongCoSoViPham': viphamATTP,
            }
          : {
              'SoLuongCXKyCamKet': dncGCN2,
              'SoLuongKiemTraThamDinh': csTD2,
              'SoLuongKhieuNai': paATTP2,
              'SoLuongCoSoViPham': viphamATTP2,
            };

      // Gán dữ liệu
      for (var item in data) {
        final String loaiThongKe = item['LoaiThongKe'];
        final int soHoSoThangNay = item['SoHoSoThangNay'];

        if (mapping.containsKey(loaiThongKe)) {
          mapping[loaiThongKe]!.value = '$soHoSoThangNay';
        } else {
          logger.w('Không tìm thấy loại thống kê: $loaiThongKe');
        }
      }

      // Log kết quả
      logger.i(
          'Dữ liệu thống kê card $cardType: ${mapping.entries.map((e) => '${e.key}: ${e.value.value}')}');
    } catch (e, stackTrace) {}
  }

  Future<void> fetchNews(int limit) async {
    try {
      final newsService = NewsService();
      final List<NewsItem> fetchedNews =
          await newsService.fetchNews(limit: limit);
      newsList.assignAll(fetchedNews);
      logger.i('Tin tức đã được tải thành công');
    } catch (e, stackTrace) {}
  }

  void updateFilterTab1(String selectedLabel) {
    final selectedFilter = filterOptions.firstWhere(
      (option) => option['label'] == selectedLabel,
    );
    currentFilteCh_tb1.value = selectedFilter['label']!;
    date.value = selectedFilter['value']!;
    fetchDanhSachThamDinh("CapGCN", danhSachThamDinh);
  }

  void updateFilterChar_Tab1(String selected) {
    currentFilterChar_tab1.value = selected;
    fetchChar1(selected);
  }

  void updateFilterChar_Tab2(String selected) {
    currentFilterChar_tab2.value = selected;
    fetchChar2(); // Gọi lại API để cập nhật biểu đồ Tab 2
  }

  void updateFilterTab2(String selectedLabel) {
    final selectedFilter = filterOptions.firstWhere(
      (option) => option['label'] == selectedLabel,
    );
    currentFilteCh_tb2.value = selectedFilter['label']!; // Cập nhật giá trị
    date.value = selectedFilter['value']!; // Gán giá trị tương ứng
    fetchDanhSachThamDinh("KyGCK", danhSachThamDinhTab2); // Gọi API cho Tab2
    logger.i(
        'Selected filter for Tab2: ${currentFilterChar_tab1.value}, Date: ${date.value}');
  }

  Future<void> checkPermission() async {
    try {
      final List<PermissionResponseModel> permissions =
          await PermissionUseCase.getPermissions();

      listFilterCategoryModel.clear();
      for (var permission in permissions) {
        var matchedCategory = listCategoryModel.firstWhere(
          (category) => category.title == permission.tenMenu,
          orElse: () => ItemCategoryFunctionModel(
            title: "",
            iconUrl: "",
            toPagePath: "",
          ),
        );
        if (matchedCategory.title.isNotEmpty) {
          listFilterCategoryModel.add(matchedCategory);
        }
      }

      logger.i('Danh sách quyền đã được xử lý');
    } catch (e, stackTrace) {}
  }

  List<ItemCategoryFunctionModel> listFilterCategoryModel = [];
  final List<ItemCategoryFunctionModel> listCategoryModel = [
    ItemCategoryFunctionModel(
        title: "Tra cứu cung lao động",
        iconUrl: AppImageString.iHomeIcon,
        toPagePath: Routes.traCuuList),
    ItemCategoryFunctionModel(
        title: "Bảng đồ cung lao động",
        iconUrl: AppImageString.iconCategoryFunction4,
        toPagePath: Routes.thongKeList),
    ItemCategoryFunctionModel(
        title: "Thống kê cung lao động",
        iconUrl: AppImageString.iconCategoryFunction1,
        toPagePath: Routes.traCuuBanDoSoList),
    ItemCategoryFunctionModel(
        title: "Tra cứu cầu lao động",
        iconUrl: AppImageString.iconCategoryFunction5,
        toPagePath: Routes.danhSachPhanAnh),
    ItemCategoryFunctionModel(
        title: "Bảng đồ cầu lao động",
        iconUrl: AppImageString.iconCategoryFunction6,
        toPagePath: Routes.phanHoiCSSXKD),
    ItemCategoryFunctionModel(
        title: "Thống kê cầu lao động",
        iconUrl: AppImageString.iconCategoryFunction7,
        toPagePath: Routes.suggestion),
    ItemCategoryFunctionModel(
        title: "Thu thập cầu lao động ban đầu",
        iconUrl: AppImageString.iconCategoryFunction4,
        toPagePath: Routes.cauLaoDongBanDau),
    ItemCategoryFunctionModel(
        title: "Thu thập cầu lao động biến động",
        iconUrl: AppImageString.iconCategoryFunction4,
        toPagePath: Routes.cauLaoDongBienDong),
  ];
}
