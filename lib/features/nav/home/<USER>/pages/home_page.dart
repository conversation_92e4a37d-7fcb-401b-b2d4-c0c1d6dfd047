// import 'package:attp_2024/core/ui/widgets/chart/custom_pie_chart.dart';
// import 'package:attp_2024/core/ui/widgets/customCachedImage/customCachedImage.dart';
// import 'package:attp_2024/core/utils/color_utils.dart';
// import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
// import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';
// import 'package:attp_2024/features/nav/home/<USER>/CertificationChartWidget.dart';
// import 'package:attp_2024/features/nav/home/<USER>/RibbonWidget.dart';
// import 'package:attp_2024/features/tin_tuc/presentation/controller/tinTuc_controller.dart';
// import 'package:attp_2024/features/tin_tuc/presentation/page/tinTuc_detail.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:get/get.dart';
// import 'package:responsive_sizer/responsive_sizer.dart';
// import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
// import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
// import 'package:attp_2024/core/configs/theme/app_colors.dart';
// import 'package:attp_2024/core/routes/routes.dart';
// import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
// import 'package:attp_2024/features/nav/home/<USER>/controllers/home_controller.dart';
// import 'package:attp_2024/features/tin_tuc/di/tinTuc_binding.dart';

// class HomePage extends GetView<HomeController> {
//   const HomePage({super.key});
//   @override
//   Widget build(BuildContext context) {
//     return DefaultTabController(
//       length: 2,
//       child: Container(
//         color: AppColors.primary,
//         child: SafeArea(
//           child: Scaffold(
//             backgroundColor: AppColors.backgroundColorPrimary,
//             body: Column(
//               children: [
//                 _buildAppBar(),
//                 Expanded(
//                   child: TabBarView(
//                     children: [
//                       SingleChildScrollView(
//                         child: Padding(
//                           padding: const EdgeInsets.all(8.0),
//                           child: Column(
//                             children: [
//                               Obx(
//                                 () => _BuildListGcn(
//                                   card_1Name: "Đề nghị cấp GCN",
//                                   card_2Name: "Phản ánh ATTP",
//                                   card_3Name: "Cơ sở thẩm định",
//                                   card_4Name: "Vi phạm ATTP",
//                                   dncGCNValue: controller.dncGCN.value,
//                                   paATTPValue: controller.paATTP.value,
//                                   csTDValue: controller.csTD.value,
//                                   viphamATTPValue: controller.viphamATTP.value,
//                                 ),
//                               ),
//                               Obx(
//                                 () => CertificationChartWidget(
//                                   title: "Cấp giấy chứng nhận",
//                                   total: controller.certificationValues
//                                       .map((e) => e.toDouble())
//                                       .reduce(
//                                           (value, element) => value + element),
//                                   values: controller.certificationValues
//                                       .map((e) => e.toDouble())
//                                       .toList(),
//                                   labels: const [
//                                     "Cấp mới",
//                                     "Cấp lại",
//                                     "Thu hồi",
//                                     "Hết hạn"
//                                   ],
//                                   colors: const [
//                                     Colors.blue,
//                                     Colors.green,
//                                     Colors.purple,
//                                     Colors.red
//                                   ],
//                                   menuItems: controller.filterOptions2
//                                       .map((e) => e['label']!)
//                                       .toList(),
//                                   selectedMenu:
//                                       controller.currentFilterChar_tab1.value,
//                                   onMenuChanged: (value) {
//                                     controller.updateFilterChar_Tab1(
//                                         value); // Update filter in controller
//                                   },
//                                 ),
//                               ),
//                               _BuildListCard(),
//                               _BuildCategoryFunction(
//                                   listItemCategory:
//                                       controller.listCategoryModel),
//                               Gap(1.h),
//                               const _BuildListNews(),
//                               Gap(10.h),
//                             ],
//                           ),
//                         ),
//                       ),
//                       SingleChildScrollView(
//                         child: Padding(
//                           padding: const EdgeInsets.all(8.0),
//                           child: Column(
//                             children: [
//                               Obx(
//                                 () => _BuildListGcn(
//                                   card_1Name: "Ký cam kết ATTP",
//                                   card_2Name: "Phản ánh ATTP",
//                                   card_3Name: "Kiểm tra ATTP",
//                                   card_4Name: "Vi phạm ATTP",
//                                   dncGCNValue: controller.dncGCN2.value,
//                                   paATTPValue: controller.paATTP2.value,
//                                   csTDValue: controller.csTD2.value,
//                                   viphamATTPValue: controller.viphamATTP2.value,
//                                 ),
//                               ),
//                               Obx(
//                                 () => CertificationChartWidget(
//                                   title: "Ký giấy cam kết",
//                                   total: controller.certificationValues1
//                                       .map((e) => e.toDouble())
//                                       .reduce(
//                                           (value, element) => value + element),
//                                   values: controller.certificationValues1
//                                       .map((e) => e.toDouble())
//                                       .toList(),
//                                   labels: const ["Cấp mới", "Hết hạn"],
//                                   colors: const [
//                                     Colors.blue,
//                                     Colors.green,
//                                   ],
//                                   menuItems: controller.filterOptions2
//                                       .map((e) => e['label']!)
//                                       .toList(),
//                                   selectedMenu:
//                                       controller.currentFilterChar_tab2.value,
//                                   onMenuChanged: (value) {
//                                     controller.updateFilterChar_Tab2(
//                                         value); // Update filter in controller
//                                   },
//                                 ),
//                               ),
//                               _BuildListCardTab2(),
//                               _BuildCategoryFunction(
//                                   listItemCategory:
//                                       controller.listCategoryModel),
//                               Gap(1.h),
//                               const _BuildListNews(),
//                               Gap(10.h),
//                               Gap(10.h),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   _buildListTag() {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(5),
//         color: Colors.white,
//         boxShadow: const [],
//       ),
//       child: SizedBox(
//         width: 100.w,
//         child: Wrap(
//           runSpacing: 8,
//           alignment: WrapAlignment.spaceBetween,
//           children: [
//             _buildTagItem(
//                 title: "Hồ sơ đề nghị cấp",
//                 // icon: CupertinoIcons.person_circle_fill,
//                 icon: AppImageString.iconHome_1,
//                 qualityPerson: 0,
//                 color: AppColors.accentColor),
//             _buildTagItem(
//                 title: "Cơ sở được thẩm định",
//                 // icon: CupertinoIcons.person_circle_fill,
//                 icon: AppImageString.iconHome_2,
//                 qualityPerson: 0,
//                 color: Colors.blue.shade800),
//             _buildTagItem(
//               title: "Trường hợp phản ánh",
//               // icon: CupertinoIcons.person_circle_fill,
//               icon: AppImageString.iconHome_3,
//               qualityPerson: 0,
//               color: Colors.purple.shade300,
//             ),
//             _buildTagItem(
//               title: "Vi phạm bị phát hiện",
//               // icon: CupertinoIcons.person_circle_fill,
//               icon: AppImageString.iconHome_4,
//               qualityPerson: 0,
//               color: AppColors.orange,
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Container _buildTagItem({
//     required String title,
//     required String icon,
//     required int qualityPerson,
//     Color color = AppColors.blue,
//     bool fullRow = false,
//   }) {
//     return Container(
//       width: !fullRow ? 50.w - 20 : Get.width,
//       padding: const EdgeInsets.symmetric(vertical: 22, horizontal: 18),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(5),
//         gradient: LinearGradient(
//           // ignore: deprecated_member_use
//           colors: [color.withOpacity(1), color.withOpacity(0.5)],
//           begin: Alignment.topLeft,
//           end: Alignment.bottomRight,
//         ),
//         boxShadow: [
//           BoxShadow(
//             // ignore: deprecated_member_use
//             color: color.withOpacity(0.5),
//             blurRadius: 8,
//             offset: const Offset(2, 4),
//           )
//         ],
//       ),
//       child: Column(
//         children: [
//           Row(
//             children: [
//               Image.asset(
//                 icon,
//                 width: 40,
//                 height: 40,
//               ),
//               const Spacer(),
//               TextWidget(
//                 text: qualityPerson.toString(),
//                 size: 24,
//                 fontWeight: FontWeight.bold,
//                 color: AppColors.white,
//               ),
//             ],
//           ),
//           const Gap(10),
//           Center(
//             child: TextWidget(
//               text: title,
//               textAlign: TextAlign.center,
//               color: AppColors.white,
//               fontWeight: FontWeight.w500,
//               size: 14,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Wrap _buildListCategory() {
//     return Wrap(
//       spacing: 5,
//       runSpacing: 5,
//       alignment: WrapAlignment.start,
//       children: [
//         _buildItemCategory(
//           title: "Giấy chứng nhận ATTP",
//           icon: CupertinoIcons.person_circle_fill,
//           color: AppColors.primary,
//           isSelected: true,
//         ),
//         _buildItemCategory(
//           title: "GCK đảm bảo ATTP",
//           icon: CupertinoIcons.building_2_fill,
//           color: AppColors.gray2,
//           isSelected: false,
//         ),
//       ],
//     );
//   }

//   Widget _buildItemCategory({
//     required String title,
//     required IconData icon,
//     required Color color,
//     required bool isSelected,
//   }) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 5),
//       decoration: BoxDecoration(
//         color: isSelected ? color : Colors.white, // Dynamic background
//         borderRadius:
//             BorderRadius.circular(20), // Rounded corners for pill effect
//         border: Border.all(
//           color: isSelected
//               ? Colors.transparent
//               : AppColors.gray2, // Border for non-selected
//           width: 1,
//         ),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Icon(
//             icon,
//             size: 16,
//             color: isSelected ? Colors.white : AppColors.gray2,
//           ),
//           const SizedBox(width: 8),
//           TextWidget(
//             text: title,
//             size: AppDimens.textSize14,
//             fontWeight: FontWeight.w600,
//             color: isSelected ? Colors.white : AppColors.gray2, // Text color
//           ),
//         ],
//       ),
//     );
//   }

//   TextField _buildSearchField() {
//     return TextField(
//       decoration: InputDecoration(
//         contentPadding: const EdgeInsets.symmetric(vertical: 0),
//         hintText: "Phường 1, Thành phố Bạc Liêu",
//         hintStyle: const TextStyle(
//             fontSize: AppDimens.textSize14,
//             fontWeight: FontWeight.w400,
//             color: AppColors.grey),
//         suffixIcon: const Icon(
//           Icons.arrow_forward_ios_rounded,
//           color: AppColors.lightThemePrimaryText,
//           size: AppDimens.textSize18,
//         ),
//         prefixIcon: const Icon(
//           CupertinoIcons.search,
//           color: AppColors.lightThemePrimaryText,
//           size: AppDimens.textSize20,
//         ),
//         enabledBorder: OutlineInputBorder(
//           borderSide: const BorderSide(width: 1, color: AppColors.gray1),
//           borderRadius: BorderRadius.circular(10),
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderSide: BorderSide(width: 1, color: AppColors.primary),
//           borderRadius: BorderRadius.circular(10),
//         ),
//       ),
//     );
//   }

//   Widget _buildAppBar() {
//     return Column(
//       children: [
//         Container(
//           color: AppColors.primary,
//           padding: EdgeInsets.symmetric(horizontal: 3.w),
//           child: Column(
//             children: [
//               Obx(() {
//                 String path = controller.avatarPathOld.value;
//                 String newPath = path.replaceFirst('~', '');
//                 return Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Row(
//                       spacing: 3.w,
//                       children: [
//                         ClipRRect(
//                           borderRadius: BorderRadius.circular(50.0),
//                           child: CustomCachedImage(
//                             width: 11.w,
//                             height: 5.h,
//                             imageUrl:
//                                 '${controller.userAccessModel.value.siteURL}/$newPath',
//                             defaultImage: AppImageString.defaultProfile,
//                           ),
//                         ),
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             TextWidget(
//                               text: controller.name.value,
//                               size: 16.sp,
//                               color: AppColors.white,
//                               fontWeight: FontWeight.w700,
//                             ),
//                             TextWidget(
//                               text: controller.userAccessModel.value.TenDonVi,
//                               color: AppColors.white,
//                               size: 15.sp,
//                               wordLimit: 8,
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                     Icon(
//                       CupertinoIcons.bell,
//                       color: AppColors.white,
//                       size: 21.sp,
//                     ),
//                   ],
//                 );
//               }),
//               // Row(
//               //   children: [
//               //     Obx(() {
//               //       String path = controller.userAccessModel.value.anhDaiDien;
//               //       String newPath = path.replaceFirst('~', '');
//               //       return ClipRRect(
//               //         borderRadius: BorderRadius.circular(50.0),
//               //         child: CustomCachedImage(
//               //           width: 11.w,
//               //           height: 5.h,
//               //           imageUrl:
//               //               '${controller.userAccessModel.value.siteURL}/$newPath',
//               //           defaultImage: AppImageString.defaultProfile,
//               //         ),
//               //       );
//               //     }),
//               //     Gap(3.w),
//               //     Obx(() => Column(
//               //           crossAxisAlignment: CrossAxisAlignment.start,
//               //           children: [
//               //             TextWidget(
//               //               text: controller.userAccessModel.value.hoTen,
//               //               size: 18.sp,
//               //               color: AppColors.white,
//               //               fontWeight: FontWeight.w700,
//               //             ),
//               //             TextWidget(
//               //               text: controller.userAccessModel.value.TenDonVi,
//               //               color: AppColors.white,
//               //               size: 15.sp,
//               //             ),
//               //           ],
//               //         )),
//               //     const Spacer(),
//               //     Icon(
//               //       CupertinoIcons.bell,
//               //       color: AppColors.white,
//               //       size: AppDimens.textSize22.sp,
//               //     ),
//               //   ],
//               // ),
//               TabBar(
//                 isScrollable: true,
//                 tabAlignment: TabAlignment.start,
//                 labelColor: AppColors.white,
//                 unselectedLabelColor: AppColors.white,
//                 indicator: const UnderlineTabIndicator(
//                   borderSide: BorderSide(width: 2.0, color: Colors.white),
//                 ),
//                 tabs: [
//                   Tab(
//                     child: Text(
//                       "Cơ sở cấp CGN ATTP",
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                         fontSize: AppDimens.textSize15.sp,
//                       ),
//                     ),
//                   ),
//                   Tab(
//                     child: Text(
//                       "Cơ sở ký GCK ATTP",
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                         fontSize: AppDimens.textSize15.sp,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Widget HomePageTabs() {
//     return DefaultTabController(
//       length: 2, // Số lượng tabs
//       child: Column(
//         children: [
//           // TabBar
//           TabBar(
//             labelColor: AppColors.primary, // Màu tab đang chọn
//             unselectedLabelColor: AppColors.gray2, // Màu tab chưa chọn
//             indicatorColor: AppColors.primary, // Màu gạch chân
//             tabs: const [
//               Tab(
//                 child: Text(
//                   "Hồ sơ đề nghị cấp GCN",
//                   style: TextStyle(fontWeight: FontWeight.bold),
//                 ),
//               ),
//               Tab(
//                 child: Text(
//                   "Hồ sơ khác",
//                   style: TextStyle(fontWeight: FontWeight.bold),
//                 ),
//               ),
//             ],
//           ),
//           // Nội dung của các tab
//           const Expanded(
//             child: TabBarView(
//               children: [
//                 Center(
//                   child: Text("Nội dung Tab 1 - Hồ sơ đề nghị cấp GCN"),
//                 ),
//                 Center(
//                   child: Text("Nội dung Tab 2 - Hồ sơ khác"),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _BuildCapGcn() {
//     return Padding(
//       padding: const EdgeInsets.all(5.0),
//       child: Container(
//         padding: const EdgeInsets.all(12),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(5),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.grey.withOpacity(0.2),
//               blurRadius: 8,
//               offset: const Offset(0, 4),
//             ),
//           ],
//         ),
//         child: const CustomPieChart(
//           values: [30, 40, 20, 10],
//           labels: ['Category A', 'Category B', 'Category C', 'Category D'],
//           title: 'Pie Chart Example',
//           sumary: 100,
//           // Total value
//           colors: [Colors.blue, Colors.green, Colors.orange, Colors.red],
//           // Custom colors
//           aspectRatio: 1.5,
//           centerSpaceRadius: 40,
//           sectionRadius: 30,
//           sectionSpace: 2,
//           legendSpacing: 12.0,
//           fontSize: 12.0,
//           legendBoxSize: 16.0,
//           legendBorderRadius: 4.0,
//           horizontalSpace: 20,
//           verticalSpace: 20,
//         ),
//       ),
//     );
//   }

//   // ignore: non_constant_identifier_names
//   Widget _BuildListGcn({
//     required String dncGCNValue,
//     required String paATTPValue,
//     required String csTDValue,
//     required String viphamATTPValue,
//     required String card_1Name,
//     required String card_2Name,
//     required String card_3Name,
//     required String card_4Name,
//   }) {
//     return Padding(
//       padding: const EdgeInsets.all(5.0),
//       child: Container(
//         padding: const EdgeInsets.all(5),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(5),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.grey.withOpacity(0.2),
//               blurRadius: 8,
//               offset: const Offset(0, 4),
//             ),
//           ],
//         ),
//         child: Column(
//           children: [
//             _buildGridRow([
//               _buildGridItem(
//                 icon: Icons.description,
//                 title: card_1Name,
//                 value: dncGCNValue,
//                 subtitle: "hồ sơ",
//               ),
//               _buildGridItem(
//                 icon: Icons.phone_android,
//                 title: card_2Name,
//                 value: paATTPValue,
//                 subtitle: "trường hợp",
//               ),
//             ]),
//             Divider(thickness: 1, color: AppColors.primary),
//             _buildGridRow([
//               _buildGridItem(
//                 icon: Icons.approval,
//                 title: card_3Name,
//                 value: csTDValue,
//                 subtitle: "cơ sở",
//               ),
//               _buildGridItem(
//                 icon: Icons.warning_amber_rounded,
//                 title: card_4Name,
//                 value: viphamATTPValue,
//                 subtitle: "trường hợp",
//               ),
//             ]),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildGridRow(List<Widget> items) {
//     List<Widget> rowChildren = [];
//     for (int i = 0; i < items.length; i++) {
//       rowChildren.add(Expanded(child: items[i]));
//       if (i < items.length - 1) {
//         rowChildren
//             .add(VerticalDivider(thickness: 1, color: AppColors.primary));
//       }
//     }
//     return Row(
//       children: rowChildren,
//     );
//   }

//   Widget _buildGridItem({
//     required IconData icon,
//     required String title,
//     required String value,
//     required String subtitle,
//   }) {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Row(
//           children: [
//             Icon(icon, size: 25.sp, color: AppColors.primary),
//             const SizedBox(width: 12),
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     title,
//                     style: TextStyle(
//                       fontSize: 15.sp,
//                       fontWeight: FontWeight.w400,
//                       color: AppColors.black,
//                     ),
//                   ),
//                   Text.rich(
//                     TextSpan(
//                       text: value,
//                       style: TextStyle(
//                         fontSize: 16.sp,
//                         fontWeight: FontWeight.w900,
//                         color: AppColors.primary,
//                       ),
//                       children: [
//                         TextSpan(
//                           text: " $subtitle",
//                           style: TextStyle(
//                             fontSize: 15.sp,
//                             fontWeight: FontWeight.normal,
//                             color: AppColors.black,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }

//   Widget _BuildListCard() {
//     return Padding(
//       padding: const EdgeInsets.all(1.0),
//       child: Stack(
//         clipBehavior: Clip
//             .antiAliasWithSaveLayer, // Cho phép hiển thị Ribbon bên ngoài Stack
//         children: [
//           Card(
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(10),
//             ),
//             elevation: 3,
//             child: Padding(
//               padding: EdgeInsets.only(left: 3.w, right: 3.w, bottom: 10),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Header
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Gap(5.w), // Để dành khoảng trống cho Ribbon
//                       Text(
//                         "Kế hoạch kiểm tra, thẩm định",
//                         style: TextStyle(
//                           fontWeight: FontWeight.bold,
//                           fontSize: 15.sp,
//                           color: AppColors.primary,
//                         ),
//                       ),
//                       const Spacer(),
//                       Obx(() {
//                         return DropdownButton<String>(
//                           value: controller.currentFilteCh_tb1.value,
//                           items: controller.filterOptions.map((option) {
//                             return DropdownMenuItem<String>(
//                               value: option['label'],
//                               child: Text(
//                                 option['label']!,
//                                 style: TextStyle(fontSize: 15.sp),
//                               ),
//                             );
//                           }).toList(),
//                           onChanged: (value) {
//                             if (value != null) {
//                               controller.updateFilterTab1(value);
//                             }
//                           },
//                           underline: const SizedBox(),
//                         );
//                       }),
//                     ],
//                   ),
//                   Gap(2.h),
//                   // List Items
//                   Obx(() {
//                     if (controller.danhSachThamDinh.isEmpty) {
//                       return Center(
//                         child: Text(
//                           "Không có dữ liệu thẩm định",
//                           style: TextStyle(color: Colors.grey, fontSize: 15.sp),
//                         ),
//                       );
//                     }

//                     return ListView.separated(
//                       shrinkWrap: true,
//                       physics: const NeverScrollableScrollPhysics(),
//                       itemCount: controller.danhSachThamDinh.length,
//                       separatorBuilder: (context, index) => const Gap(8),
//                       itemBuilder: (context, index) {
//                         final item = controller.danhSachThamDinh[index];
//                         return _buildTaskItem(
//                             date: item.ngayThamDinh,
//                             code: item.soBienBan,
//                             company: item.tenCoSo,
//                             status: item.xepLoai,
//                             color: item.mauXepLoai);
//                       },
//                     );
//                   }),
//                 ],
//               ),
//             ),
//           ),
//           // Ribbon dán sát mép trái của Card
//           Positioned(
//             top: .5.h, // Điều chỉnh cho ribbon bám sát mép trên của card
//             left: 3.w, // Điều chỉnh để bám sát mép trái
//             child: RibbonWidget(
//               color: AppColors.primary,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//   // Widget _BuildListCard() {
//   //   return Padding(
//   //     padding: const EdgeInsets.all(1.0),
//   //     child: Card(
//   //       shape: RoundedRectangleBorder(
//   //         borderRadius: BorderRadius.circular(10),
//   //       ),
//   //       elevation: 3,
//   //       child: Padding(
//   //         padding: EdgeInsets.only(left: 3.w, right: 3.w, bottom: 10),
//   //         child: Column(
//   //           crossAxisAlignment: CrossAxisAlignment.start,
//   //           children: [
//   //             // Header
//   //             Row(
//   //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//   //               children: [
//   //                 Row(
//   //                   children: [
//   //                     RibbonWidget(
//   //                       color: AppColors.primary,
//   //                     ),
//   //                     Gap(1.w),
//   //                     Text(
//   //                       "Kế hoạch kiểm tra, thẩm định",
//   //                       style: TextStyle(
//   //                         fontWeight: FontWeight.bold,
//   //                         fontSize: 16.sp,
//   //                         color: AppColors.primary,
//   //                       ),
//   //                     ),
//   //                   ],
//   //                 ),
//   //                 Obx(() {
//   //                   return DropdownButton<String>(
//   //                     value: controller.currentFilteCh_tb1
//   //                         .value, // Liên kết với giá trị observable
//   //                     items: controller.filterOptions.map((option) {
//   //                       return DropdownMenuItem<String>(
//   //                         value: option['label'], // Sử dụng `label` làm giá trị
//   //                         child: Text(
//   //                           option['label']!,
//   //                           style: TextStyle(fontSize: 16.sp),
//   //                         ), // Hiển thị label
//   //                       );
//   //                     }).toList(),
//   //                     onChanged: (value) {
//   //                       if (value != null) {
//   //                         controller.updateFilterTab1(
//   //                             value); // Gọi hàm để cập nhật bộ lọc
//   //                       }
//   //                     },
//   //                     underline: const SizedBox(), // Loại bỏ gạch chân
//   //                   );
//   //                 }),
//   //               ],
//   //             ),
//   //             Gap(2.h),
//   //             // List Items
//   //             Obx(() {
//   //               // Kiểm tra nếu danh sách rỗng
//   //               if (controller.danhSachThamDinh.isEmpty) {
//   //                 return const Center(
//   //                   child: Text(
//   //                     "Không có dữ liệu thẩm định",
//   //                     style: TextStyle(color: Colors.grey, fontSize: 16),
//   //                   ),
//   //                 );
//   //               }

//   //               return ListView.separated(
//   //                 shrinkWrap: true,
//   //                 physics: const NeverScrollableScrollPhysics(),
//   //                 itemCount: controller.danhSachThamDinh.length,
//   //                 separatorBuilder: (context, index) => const Gap(8),
//   //                 itemBuilder: (context, index) {
//   //                   final item = controller.danhSachThamDinh[index];
//   //                   return _buildTaskItem(
//   //                       date: item.ngayThamDinh,
//   //                       code: item.soBienBan,
//   //                       company: item.tenCoSo,
//   //                       status: item.xepLoai,
//   //                       color: item.mauXepLoai);
//   //                 },
//   //               );
//   //             }),
//   //           ],
//   //         ),
//   //       ),
//   //     ),
//   //   );
//   // }

//   // Widget _BuildListCardTab2() {
//   //   return Padding(
//   //     padding: const EdgeInsets.all(1.0),
//   //     child: Card(
//   //       shape: RoundedRectangleBorder(
//   //         borderRadius: BorderRadius.circular(10),
//   //       ),
//   //       elevation: 3,
//   //       child: Padding(
//   //         padding:
//   //             const EdgeInsets.only(left: 12, right: 12, bottom: 10, top: 0),
//   //         child: Column(
//   //           crossAxisAlignment: CrossAxisAlignment.start,
//   //           children: [
//   //             // Header
//   //             Row(
//   //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//   //               children: [
//   //                 Row(
//   //                   children: [
//   //                     RibbonWidget(color: AppColors.primary),
//   //                     const Gap(8),
//   //                     Text(
//   //                       "Kế hoạch kiểm tra, thẩm định",
//   //                       style: TextStyle(
//   //                           fontWeight: FontWeight.bold,
//   //                           fontSize: 16.sp,
//   //                           color: AppColors.primary),
//   //                     ),
//   //                   ],
//   //                 ),
//   //                 Gap(2.w),
//   //                 Expanded(
//   //                   // Allow DropdownButton to take only the available remaining space
//   //                   child: Obx(() {
//   //                     // Ensure DropdownButton value matches available items
//   //                     final filterOptions =
//   //                         controller.filterOptions.toSet().toList();
//   //                     final currentValue = filterOptions.any((option) =>
//   //                             option['label'] ==
//   //                             controller.currentFilteCh_tb2.value)
//   //                         ? controller.currentFilteCh_tb2.value
//   //                         : filterOptions.first['label'];

//   //                     return DropdownButton<String>(
//   //                       value: currentValue,
//   //                       items: filterOptions.map((option) {
//   //                         return DropdownMenuItem<String>(
//   //                           value: option['label'],
//   //                           child: Text(
//   //                             option['label']!,
//   //                             style: TextStyle(fontSize: 16.sp),
//   //                           ),
//   //                         );
//   //                       }).toList(),
//   //                       onChanged: (value) {
//   //                         if (value != null) {
//   //                           controller
//   //                               .updateFilterTab2(value); // Update Tab2 filter
//   //                         }
//   //                       },
//   //                       underline: const SizedBox(),
//   //                       // Remove underline styling
//   //                       isExpanded:
//   //                           true, // Ensure DropdownButton uses full width of Expanded
//   //                     );
//   //                   }),
//   //                 ),
//   //               ],
//   //             ),
//   //             const Gap(10),
//   //             // List Items
//   //             Obx(() {
//   //               // Kiểm tra nếu danh sách rỗng
//   //               if (controller.danhSachThamDinhTab2.isEmpty) {
//   //                 return const Center(
//   //                   child: Text(
//   //                     "Không có dữ liệu thẩm định",
//   //                     style: TextStyle(color: Colors.grey, fontSize: 16),
//   //                   ),
//   //                 );
//   //               }

//   //               return ListView.separated(
//   //                 shrinkWrap: true,
//   //                 physics: const NeverScrollableScrollPhysics(),
//   //                 itemCount: controller.danhSachThamDinhTab2.length,
//   //                 separatorBuilder: (context, index) => const Gap(8),
//   //                 itemBuilder: (context, index) {
//   //                   final item = controller.danhSachThamDinhTab2[index];
//   //                   return _buildTaskItem(
//   //                       date: item.ngayThamDinh,
//   //                       code: item.soBienBan,
//   //                       company: item.tenCoSo,
//   //                       status: item.xepLoai,
//   //                       color: item.mauXepLoai);
//   //                 },
//   //               );
//   //             }),
//   //           ],
//   //         ),
//   //       ),
//   //     ),
//   //   );
//   // }

//   Widget _BuildListCardTab2() {
//     return Padding(
//       padding: const EdgeInsets.all(1.0),
//       child: Stack(
//         clipBehavior: Clip
//             .antiAliasWithSaveLayer, // Để Ribbon có thể hiển thị ra ngoài Stack
//         children: [
//           Card(
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(10),
//             ),
//             elevation: 3,
//             child: Padding(
//               padding: const EdgeInsets.only(
//                   left: 12, right: 12, bottom: 10, top: 0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Header
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Gap(5.w), // Để tạo khoảng trống cho Ribbon
//                       Text(
//                         "Kế hoạch kiểm tra, thẩm định",
//                         style: TextStyle(
//                             fontWeight: FontWeight.bold,
//                             fontSize: 15.sp,
//                             color: AppColors.primary),
//                       ),
//                       Gap(2.w),
//                       Expanded(
//                         child: Obx(() {
//                           final filterOptions =
//                               controller.filterOptions.toSet().toList();
//                           final currentValue = filterOptions.any((option) =>
//                                   option['label'] ==
//                                   controller.currentFilteCh_tb2.value)
//                               ? controller.currentFilteCh_tb2.value
//                               : filterOptions.first['label'];

//                           return DropdownButton<String>(
//                             value: currentValue,
//                             items: filterOptions.map((option) {
//                               return DropdownMenuItem<String>(
//                                 value: option['label'],
//                                 child: Text(
//                                   option['label']!,
//                                   style: TextStyle(fontSize: 15.sp),
//                                 ),
//                               );
//                             }).toList(),
//                             onChanged: (value) {
//                               if (value != null) {
//                                 controller.updateFilterTab2(value);
//                               }
//                             },
//                             underline: const SizedBox(),
//                             isExpanded: true,
//                           );
//                         }),
//                       ),
//                     ],
//                   ),
//                   const Gap(10),
//                   // List Items
//                   Obx(() {
//                     if (controller.danhSachThamDinhTab2.isEmpty) {
//                       return const Center(
//                         child: Text(
//                           "Không có dữ liệu thẩm định",
//                           style: TextStyle(color: Colors.grey, fontSize: 16),
//                         ),
//                       );
//                     }

//                     return ListView.separated(
//                       shrinkWrap: true,
//                       physics: const NeverScrollableScrollPhysics(),
//                       itemCount: controller.danhSachThamDinhTab2.length,
//                       separatorBuilder: (context, index) => const Gap(8),
//                       itemBuilder: (context, index) {
//                         final item = controller.danhSachThamDinhTab2[index];
//                         return _buildTaskItem(
//                             date: item.ngayThamDinh,
//                             code: item.soBienBan,
//                             company: item.tenCoSo,
//                             status: item.xepLoai,
//                             color: item.mauXepLoai);
//                       },
//                     );
//                   }),
//                 ],
//               ),
//             ),
//           ),
//           // Ribbon dán sát mép trái của Card
//           Positioned(
//             top: .5.h, // Đưa Ribbon lên sát mép trên của Card
//             left: 3.w, // Đưa Ribbon sát mép trái
//             child: RibbonWidget(
//               color: AppColors.primary,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildTaskItem(
//       {required String date,
//       required String code,
//       required String company,
//       required String status,
//       required String color}) {
//     return Card(
//       elevation: 2,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Date and Status
//             Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   date,
//                   style: const TextStyle(
//                     fontWeight: FontWeight.w500,
//                     fontSize: 14,
//                     color: Colors.black87,
//                   ),
//                 ),
//                 const Gap(4),
//                 Container(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 8,
//                     vertical: 4,
//                   ),
//                   decoration: BoxDecoration(
//                     color: hexToColor(color),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: Text(
//                     status,
//                     style: const TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: AppDimens.textSize12,
//                       color: Colors.white,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             const Gap(12),
//             // Company Info
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     code,
//                     style: const TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: 14,
//                       color: Colors.black87,
//                     ),
//                   ),
//                   const Gap(4),
//                   Text(
//                     company,
//                     style: const TextStyle(
//                       fontSize: 13,
//                       color: Colors.black54,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   _buildHeaderListNews() {
//     return Row(
//       children: [
//         const TextWidget(
//           text: "Tin tức nổi bật",
//           fontWeight: FontWeight.w700,
//           size: AppDimens.textSize16,
//         ),
//         const Spacer(),
//         InkWell(
//           onTap: () {
//             Get.toNamed(Routes.tinTuc);
//           },
//           child: Row(
//             children: [
//               TextWidget(
//                 text: "Xem thêm",
//                 color: AppColors.primary,
//               ),
//               Icon(
//                 Icons.arrow_forward_ios_rounded,
//                 size: AppDimens.textSize16,
//                 color: AppColors.primary,
//               )
//             ],
//           ),
//         ),
//       ],
//     );
//   }
// }

// _buildHeaderListNews() {
//   return Row(
//     children: [
//       TextWidget(
//         text: "Tin tức nổi bật",
//         fontWeight: FontWeight.w700,
//         size: AppDimens.textSize15.sp,
//       ),
//       const Spacer(),
//       InkWell(
//         onTap: () {
//           Get.find<MainController>().onChangeItemBottomBar(1);
//         },
//         child: Row(
//           children: [
//             Text(
//               "Xem tất cả",
//               style: TextStyle(
//                 fontSize: AppDimens.textSize15.sp,
//                 color: AppColors.primary,
//                 decorationColor: AppColors.primary,
//                 decoration: TextDecoration.combine([
//                   TextDecoration.underline,
//                 ]),
//               ),
//             ),
//             Icon(
//               Icons.chevron_right,
//               size: AppDimens.textSize15.sp,
//               color: AppColors.primary,
//             ),
//           ],
//         ),
//       ),
//     ],
//   );
// }

// Widget _buildItemCategoryFunction(
//     {required String title,
//     required String iconsUrl,
//     required String toPagePath}) {
//   return InkWell(
//     onTap: () {
//       if (toPagePath != "") Get.toNamed(toPagePath);
//     },
//     child: Column(
//       children: [
//         Container(
//             padding: const EdgeInsets.all(10),
//             decoration: BoxDecoration(
//                 color: AppColors.white,
//                 borderRadius: BorderRadius.circular(10),
//                 boxShadow: [
//                   BoxShadow(
//                       color: AppColors.black.withOpacity(.15),
//                       spreadRadius: 1,
//                       blurRadius: 2,
//                       offset: const Offset(0, 2))
//                 ]),
//             width: 15.w,
//             height: 7.h,
//             child: Image.asset(iconsUrl)),
//         Padding(
//           padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
//           child: TextWidget(
//             textAlign: TextAlign.center,
//             text: title,
//             size: AppDimens.textSize13.sp,
//             maxLines: 2,
//           ),
//         )
//       ],
//     ),
//   );
// }

// _buildHeaderCategoryFunction() {
//   return Row(
//     children: [
//       RibbonWidget(
//         color: AppColors.primary,
//       ),
//       Gap(
//         1.w,
//       ),
//       TextWidget(
//         text: "Tiện ích",
//         size: 15.sp,
//         fontWeight: FontWeight.w600,
//         color: AppColors.primary,
//       ),
//     ],
//   );
// }

// class _BuildCategoryFunction extends StatelessWidget {
//   final List<ItemCategoryFunctionModel> listItemCategory;

//   const _BuildCategoryFunction({required this.listItemCategory});

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.all(5.0),
//       child: Container(
//         padding: EdgeInsets.symmetric(horizontal: 2.w),
//         decoration: BoxDecoration(
//             color: AppColors.white,
//             borderRadius: BorderRadius.circular(10),
//             boxShadow: [
//               BoxShadow(
//                   // ignore: deprecated_member_use
//                   color: AppColors.black.withOpacity(.15),
//                   spreadRadius: 1,
//                   blurRadius: 2,
//                   offset: const Offset(0, 2))
//             ]),
//         child: Column(
//           children: [
//             _buildHeaderCategoryFunction(),
//             Container(
//               padding: EdgeInsets.symmetric(
//                 vertical: 2.h,
//               ),
//               width: 100.w,
//               child: SizedBox(
//                 height: 26.h,
//                 child: GridView.count(
//                     physics: const NeverScrollableScrollPhysics(),
//                     crossAxisCount: 3,
//                     mainAxisSpacing: 0,
//                     children: listItemCategory.map(
//                       (items) {
//                         return _buildItemCategoryFunction(
//                             title: items.title,
//                             iconsUrl: items.iconUrl,
//                             toPagePath: items.toPagePath);
//                       },
//                     ).toList()),
//               ),
//             )
//           ],
//         ),
//       ),
//     );
//   }
// }

// class _BuildListNews extends StatelessWidget {
//   const _BuildListNews();

//   @override
//   Widget build(BuildContext context) {
//     final HomeController controller = Get.find<HomeController>();
//     return Padding(
//       padding: const EdgeInsets.all(5.0),
//       child: Column(
//         children: [
//           _buildHeaderListNews(),
//           Gap(1.h),
//           SizedBox(
//             height: 18.h,
//             child: Obx(() {
//               if (controller.newsList.isEmpty) {
//                 return const Center(
//                   child: Text(
//                     "Không có tin tức nào",
//                     style: TextStyle(fontSize: 16, color: Colors.grey),
//                   ),
//                 );
//               }
//               return ListView.builder(
//                 scrollDirection: Axis.horizontal,
//                 itemCount: controller.newsList.length,
//                 itemBuilder: (context, index) {
//                   final news = controller.newsList[index];
//                   return GestureDetector(
//                     onTap: () {
//                       Get.to(
//                         binding: TinTucBinding(),
//                         TinTucDetailPage(
//                           news: news,
//                         ),
//                       );
//                     },
//                     child: Container(
//                       width: 60.w,
//                       margin: const EdgeInsets.only(right: 15),
//                       child: Stack(
//                         alignment: Alignment.bottomLeft,
//                         children: [
//                           // Ảnh nền
//                           Obx(() {
//                             return ClipRRect(
//                               borderRadius: BorderRadius.circular(10),
//                               child: CustomCachedImage(
//                                   width: double.infinity,
//                                   imageUrl:
//                                       "${controller.userAccessModel.value.siteURL}${news.HinhAnh}",
//                                   defaultImage: AppImageString.imageNotFount,
//                                   fit: BoxFit.fill),
//                             );
//                           }),
//                           // Lớp phủ mờ nền phía dưới
//                           Positioned.fill(
//                             child: Container(
//                               decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.circular(10),
//                                 gradient: LinearGradient(
//                                   begin: Alignment.bottomCenter,
//                                   end: Alignment.topCenter,
//                                   colors: [
//                                     Colors.black.withOpacity(0.6),
//                                     Colors.transparent,
//                                   ],
//                                 ),
//                               ),
//                             ),
//                           ),
//                           Positioned(
//                             bottom: 10,
//                             left: 10,
//                             right: 10,
//                             child: Text(
//                               news.TieuDe ?? "Không có tiêu đề",
//                               maxLines: 2,
//                               overflow: TextOverflow.ellipsis,
//                               style: const TextStyle(
//                                 fontSize: 14,
//                                 fontWeight: FontWeight.bold,
//                                 color: Colors.white,
//                                 shadows: [
//                                   Shadow(
//                                     blurRadius: 4,
//                                     color: Colors.black,
//                                     offset: Offset(2, 2),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   );
//                 },
//               );
//             }),
//           ),
//         ],
//       ),
//     );
//   }
// }

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/images/image_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';
import 'package:attp_2024/features/nav/home/<USER>/controllers/home_controller.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: AppDimens.textSize16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAppBar(),
                _buildSearchField(),
                const SizedBox(height: 16),
                _buildListCategory(),
                const SizedBox(height: 16),
                _buildListTag(),
                const SizedBox(height: 16),
                _BuildCategoryFunction(
                    listItemCategory: controller.listCategoryModel),
                const _BuildListNews(),
                const SizedBox(
                  height: 100,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  SizedBox _buildListTag() {
    return SizedBox(
      width: 100.w,
      child: Wrap(
        runSpacing: 10,
        alignment: WrapAlignment.spaceBetween,
        children: [
          _buildTagItem(
              title: "Người làm việc",
              icon: CupertinoIcons.person_circle_fill,
              qualityPerson: 1203,
              color: AppColors.blue),
          _buildTagItem(
              title: "Thất nghiệp",
              icon: CupertinoIcons.person_circle_fill,
              qualityPerson: 1203,
              color: AppColors.accentColor),
          _buildTagItem(
              title: "Không tham gia hoạt động kinh tế",
              icon: CupertinoIcons.person_circle_fill,
              qualityPerson: 1203,
              color: AppColors.gray2,
              fullRow: true),
        ],
      ),
    );
  }

  Container _buildTagItem(
      {required String title,
      required IconData icon,
      required int qualityPerson,
      Color color = AppColors.blue,
      bool fullRow = false}) {
    return Container(
      width: !fullRow ? 50.w - 20 : Get.width,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        gradient: LinearGradient(
          colors: [color, color.withOpacity(.5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWidget(
                text: title,
                color: AppColors.white,
                fontWeight: FontWeight.w500,
                size: AppDimens.textSize14,
              ),
              Icon(
                icon,
                color: AppColors.white,
                size: AppDimens.textSize32,
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Row(
            children: [
              TextWidget(
                text: "$qualityPerson",
                size: AppDimens.textSize18,
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
              const SizedBox(
                width: 5,
              ),
              const TextWidget(
                size: AppDimens.textSize12,
                text: "Người",
                color: AppColors.white,
              ),
            ],
          )
        ],
      ),
    );
  }

  Wrap _buildListCategory() {
    return Wrap(
      spacing: 10,
      children: [
        _buildItemCategory(
            title: "Cung lao động",
            icon: CupertinoIcons.person_circle_fill,
            color: AppColors.blue),
        _buildItemCategory(
            title: "Cung lao động",
            icon: CupertinoIcons.building_2_fill,
            color: AppColors.gray2),
      ],
    );
  }

  FittedBox _buildItemCategory(
      {required String title,
      required IconData icon,
      Color color = AppColors.blue}) {
    return FittedBox(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
            color: color.withOpacity(.2),
            borderRadius: BorderRadius.circular(100)),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: AppDimens.textSize20,
            ),
            const SizedBox(
              width: 5,
            ),
            TextWidget(
              text: title,
              size: AppDimens.textSize14,
              fontWeight: FontWeight.w600,
              color: color,
            )
          ],
        ),
      ),
    );
  }

  TextField _buildSearchField() {
    return TextField(
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(vertical: 0),
        hintText: "Phường 1, Thành phố Bạc Liêu",
        hintStyle: const TextStyle(
            fontSize: AppDimens.textSize14,
            fontWeight: FontWeight.w400,
            color: AppColors.grey),
        suffixIcon: const Icon(
          Icons.arrow_forward_ios_rounded,
          color: AppColors.lightThemePrimaryText,
          size: AppDimens.textSize18,
        ),
        prefixIcon: const Icon(
          CupertinoIcons.search,
          color: AppColors.lightThemePrimaryText,
          size: AppDimens.textSize20,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.gray1),
          borderRadius: BorderRadius.circular(10),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(width: 1, color: AppColors.primary),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Container _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        children: [
          CircleAvatar(
            child: Image.asset(AppImageString.avatar),
          ),
          const SizedBox(
            width: 10,
          ),
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextWidget(
                text: "Bùi Thanh Hoàng ",
                size: 16,
                fontWeight: FontWeight.w700,
              ),
              TextWidget(
                text: "Người dùng cấp xã",
                size: 12,
              ),
            ],
          ),
          const Spacer(),
          const Icon(
            CupertinoIcons.bell,
            color: AppColors.lightThemePrimaryText,
            size: AppDimens.textSize24,
          )
        ],
      ),
    );
  }
}

class _BuildListNews extends StatelessWidget {
  const _BuildListNews();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeaderListNews(),
        const SizedBox(
          height: 5,
        ),
        SizedBox(
          height: 150,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 10,
            itemBuilder: (context, index) => Container(
              margin: const EdgeInsets.only(right: 15),
              child: Column(
                children: [
                  SizedBox(
                    height: 100,
                    width: 60.w, // Chiều rộng của mỗi phần tử
                    child: const ImageWidget(
                      imageUrl:
                          "https://caodang.fpt.edu.vn/wp-content/uploads/2024/06/DAP-AN-TN-THPT-900x600-13-2048x1365.jpg",
                    ),
                  ),
                  SizedBox(
                    width: 60.w,
                    child: const TextWidget(
                        size: AppDimens.textSize14,
                        maxLines: 2,
                        text:
                            "Người lao động có thể nộp tiền BHTN doanh nghiệp nợ để Người lao động có thể nộp tiền BHTN doanh nghiệp nợ để "),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Row _buildHeaderListNews() {
    return const Row(
      children: [
        Icon(
          CupertinoIcons.news_solid,
          color: AppColors.gray2,
        ),
        SizedBox(
          width: 5,
        ),
        TextWidget(
          text: "Tin tức",
          fontWeight: FontWeight.w700,
          size: AppDimens.textSize18,
        ),
      ],
    );
  }
}

class _BuildCategoryFunction extends StatelessWidget {
  final List<ItemCategoryFunctionModel> listItemCategory;
  const _BuildCategoryFunction({required this.listItemCategory});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeaderCategoryFunction(),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 15),
          width: 100.w,
          child: SizedBox(
            height: 200,
            child: GridView.count(
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 4,
                mainAxisSpacing: 10,
                children: listItemCategory.map(
                  (items) {
                    return _buildItemCategoryFunction(
                        title: items.title,
                        iconsUrl: items.iconUrl,
                        toPagePath: items.toPagePath);
                  },
                ).toList()),
          ),
        )
      ],
    );
  }

  Widget _buildItemCategoryFunction(
      {required String title,
      required String iconsUrl,
      required String toPagePath}) {
    return InkWell(
      onTap: () {
        if (toPagePath != "") Get.toNamed(toPagePath);
      },
      child: Column(
        children: [
          Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                        color: AppColors.black.withOpacity(.15),
                        spreadRadius: 1,
                        blurRadius: 2,
                        offset: const Offset(0, 2))
                  ]),
              width: 40,
              height: 40,
              child: Image.asset(iconsUrl)),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
            child: TextWidget(
              textAlign: TextAlign.center,
              text: title,
              size: AppDimens.textSize10,
              maxLines: 2,
            ),
          )
        ],
      ),
    );
  }

  Row _buildHeaderCategoryFunction() {
    return Row(
      children: [
        const Icon(
          CupertinoIcons.list_bullet,
          size: AppDimens.textSize20,
        ),
        const SizedBox(
          width: 5,
        ),
        const TextWidget(
          text: "Chức năng",
          size: AppDimens.textSize18,
          fontWeight: FontWeight.w700,
        ),
        const Spacer(),
        InkWell(
          onTap: () {
            Get.toNamed("/thu_thap_cung_ld_bd");
          },
          child: Row(
            children: [
              TextWidget(
                text: "Tùy chỉnh",
                color: AppColors.primary,
              ),
              Icon(
                Icons.arrow_forward_ios_rounded,
                size: AppDimens.textSize16,
                color: AppColors.primary,
              )
            ],
          ),
        )
      ],
    );
  }
}
