import 'package:attp_2024/features/main/presentation/controller/main_controller.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';

import '../../../../../../core/data/dto/response/permission_response.dart';
import '../../../../../../core/services/permission_use_case.dart';

class TracuulistController extends GetxController {
  MainController? mainController;
  int selectedIndex = 0;

  @override
  void onInit() async {
    super.onInit();
    mainController = Get.find<MainController>();
    selectedIndex = mainController!.selectedPageIndex.value;
    // await checkPermission();
    await loadInfoProfile();
  }

  UserAccessModel? userAccessModel;
  final displayName = ''.obs;

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    displayName.value = userAccessModel!.hoTen;
    update(["bodyID"]);
  }

  Future<void> checkPermission() async {
    List<PermissionResponseModel> checkPermission =
        await PermissionUseCase.getPermissions();
    listFilterCategoryModel.clear();
    for (var permission in checkPermission) {
      var matchedCategory = listCategoryModel.firstWhere(
        (category) => category.title == permission.tenMenu,
        orElse: () =>
            ItemCategoryFunctionModel(title: "", iconUrl: "", toPagePath: ""),
      );
      if (matchedCategory.title.isNotEmpty) {
        listFilterCategoryModel.add(matchedCategory);
      }
    }
  }

  List<ItemCategoryFunctionModel> listFilterCategoryModel = [];

  final List<ItemCategoryFunctionModel> listCategoryModel = [
    ItemCategoryFunctionModel(
        title: "Cơ sở sản xuất kinh doanh thuộc diện cấp giấy chứng nhận ATTP",
        iconUrl: AppImageString.coSoGCNImg,
        toPagePath: Routes.coSoDuDieuKien),
    ItemCategoryFunctionModel(
        title:
            "Cơ sở sản xuất kinh doanh không thuộc diện cấp giấy chứng nhận ATTP",
        iconUrl: AppImageString.coSoGCKImg,
        toPagePath: Routes.coSoKhongDuDieuKien),
    ItemCategoryFunctionModel(
        title: "Giấy chứng nhận ATTP",
        iconUrl: AppImageString.ttGCNImg,
        toPagePath: Routes.thongTinGCN),
    ItemCategoryFunctionModel(
        title: "Bản cam kết đảm bảo ATTP",
        iconUrl: AppImageString.ttGCKImg,
        toPagePath: Routes.giayCamKet),
    ItemCategoryFunctionModel(
        title: "Giấy xác nhận tập huấn kiến thức ATTP",
        iconUrl: AppImageString.ttGXNKTImg,
        toPagePath: Routes.thongTinGiayXacNhanKienThucATTP),
    ItemCategoryFunctionModel(
        title: "Kết quả kiểm tra ATTP",
        iconUrl: AppImageString.kqKTImg,
        toPagePath: Routes.thongTinketQuaKiemTra),
  ];
}
