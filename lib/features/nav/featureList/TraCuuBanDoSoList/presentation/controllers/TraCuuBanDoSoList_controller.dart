import 'package:get/get.dart';
import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:attp_2024/features/nav/home/<USER>/item_category_function_model.dart';

import '../../../../../../core/data/dto/response/permission_response.dart';
import '../../../../../../core/services/permission_use_case.dart';


class TracuubandosolistController extends GetxController {
  @override
  void onInit() async {
    super.onInit();
    await checkPermission();
    await loadInfoProfile();
  }

  UserAccessModel? userAccessModel;
  final displayName = ''.obs;

  Future<void> loadInfoProfile() async {
    userAccessModel = await UserUseCase.getUser();
    displayName.value = userAccessModel!.hoTen;
    update(["bodyID"]);
  }

  Future<void> checkPermission() async {
    List<PermissionResponseModel> checkPermission =
        await PermissionUseCase.getPermissions();
    listFilterCategoryModel.clear();
    for (var permission in checkPermission) {
      var matchedCategory = listCategoryModel.firstWhere(
        (category) => category.title == permission.tenMenu,
        orElse: () =>
            ItemCategoryFunctionModel(title: "", iconUrl: "", toPagePath: ""),
      );
      if (matchedCategory.title.isNotEmpty) {
        listFilterCategoryModel.add(matchedCategory);
      }
    }
    // In ra danh sách đã lọc để kiểm tra
    print(listFilterCategoryModel);
  }

  List<ItemCategoryFunctionModel> listFilterCategoryModel = [];

  final List<ItemCategoryFunctionModel> listCategoryModel = [
    ItemCategoryFunctionModel(
        title: "Cơ sở thuộc diện cấp giấy chứng nhận",
        iconUrl: AppImageString.mapscssxkd,
        toPagePath: Routes.traCuuCSSXKDDuDKATTPBanDoSo),
    ItemCategoryFunctionModel(
        title: "Cơ sở không thuộc diện cấp giấy chứng nhận",
        iconUrl: AppImageString.mapscssxkd,
        toPagePath: Routes.traCuuCSSXKDChuaDuDKATTPBanDoSo),
  ];
}
