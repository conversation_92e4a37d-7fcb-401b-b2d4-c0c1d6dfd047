import 'package:attp_2024/core/configs/assets/images/app_image_string.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:attp_2024/features/nav/profile/presentation/controllers/profile_controller.dart';
import '../../../../../core/ui/widgets/customCachedImage/customCachedImage.dart';

class ProfilePage extends GetView<ProfileController> {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gray1,
      body: GetBuilder<ProfileController>(
        id: "bodyID",
        builder: (_) => _buildAll(),
      ),
    );
  }

  Widget _buildAll() {
    return SingleChildScrollView(
      child: Stack(
        children: [
          _buildBackground(),
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                _buildBody(),
                _buildMenu(),
                _buildFooter(),
                SizedBox(
                  height: 10.h,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Column(
      children: [
        SizedBox(
          height: 24.h, // Use this as the height of your image
          child: Image.asset(
            AppImageString.backgroundProfile,
            fit: BoxFit.cover,
            width: Device.safeWidth, // Chiều rộng
            // height: 40, // Chiều cao
          ),
        ), // Background section color
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      margin: const EdgeInsets.only(top: 20, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //   children: [
          //     Row(
          //       children: [
          //         Icon(Icons.account_balance_outlined,
          //             color: Colors.yellowAccent, size: 20.sp),
          //         const Gap(5),
          //         FutureBuilder<String>(
          //           future: controller.getCurrentLocation(),
          //           builder: (context, snapshot) {
          //             if (snapshot.connectionState == ConnectionState.waiting) {
          //               return const CircularProgressIndicator(); // Show loading
          //             } else if (snapshot.hasError) {
          //               return const Text(
          //                 "Lỗi lấy vị trí",
          //                 style: TextStyle(color: Colors.red),
          //               ); // Handle errors
          //             } else {
          //               return TextWidget(
          //                 text: snapshot.data ?? "Vị trí không xác định",
          //                 size: AppDimens.defaultText,
          //                 fontWeight: FontWeight.bold,
          //                 color: AppColors.white,
          //               );
          //             }
          //           },
          //         ),
          //       ],
          //     ),
          //   ],
          // ),
          // const Gap(5),
          // TextWidget(
          //     text: controller.getGreetingMessage(),
          //     size: AppDimens.textBase,
          //     color: Colors.white70),
        ],
      ),
    );
  }

  Widget _info() {
    print(
        '${controller.userAccessModel?.siteURL}${controller.avatarPathOld.value.replaceFirst('~', '')}');
    return Padding(
      padding: EdgeInsets.only(bottom: 3.w, left: 3.w, top: 2.w),
      child: Row(
        children: [
          Obx(
            () => Container(
              padding: const EdgeInsets.all(
                  2), // Khoảng cách giữa viền và CircleAvatar
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.blue,
                  width: 0.2.w,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(32.0),
                child: CustomCachedImage(
                  width: 60,
                  height: 60,
                  imageUrl:
                      '${controller.userAccessModel?.siteURL}/${controller.avatarPathOld.value.replaceFirst('~', '')}',
                  defaultImage: AppImageString.defaultProfile,
                ),
              ),
            ),
          ),
          Gap(3.w),
          Obx(() => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    text: controller.name.value,
                    size: AppDimens.largeText,
                    fontWeight: FontWeight.bold,
                    color: AppColors.roleText,
                  ),
                  const Gap(5),
                  TextWidget(
                      text: controller.userAccessModel?.TenDonVi ??
                          "Đang cập nhật",
                      size: AppDimens.textBase,
                      color: AppColors.roleText),
                ],
              )),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      margin: const EdgeInsets.only(top: 20, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _info(),
          buildDivider(),
          buildListTile("Thông tin cá nhân", Icons.info, () {
            Get.toNamed(Routes.editProfile);
          }),
          buildDivider(),
          buildListTile("Đổi mật khẩu", Icons.lock_outline, () {
            Get.toNamed(Routes.changePassword);
          }),
        ],
      ),
    );
  }

  Widget _buildMenu() {
    return Container(
      margin: const EdgeInsets.only(top: 20, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          buildListTile("Tùy chỉnh giao diện", Icons.dashboard, () {
            Get.toNamed(Routes.customInterface);
          }),
          // buildDivider(),
          // buildListTile("Thông báo", Icons.notifications, () {
          //   Get.toNamed(Routes.fingerprint);
          // }),
          buildDivider(),
          buildListTile("Trung tâm trợ giúp", Icons.help_outline, () {
            Get.toNamed(Routes.suggestion);
          }),
          buildDivider(),
          buildListTile("Đăng nhập bằng faceID / Vân tay", Icons.face_6_sharp,
              () {
            Get.toNamed(Routes.fingerprint);
          }),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      margin: const EdgeInsets.only(top: 20, left: 10, right: 10),
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          buildListTile("Thông tin ứng dụng", Icons.info, () {
            Get.toNamed(Routes.appInfo);
          }),
          buildDivider(),
          buildListTile("Thông tin nhà phát triển", Icons.lock_outline, () {
            Get.toNamed(Routes.developer);
          }),
          buildDivider(),
          buildListTile("Quyền riêng tư và bảo mật", Icons.privacy_tip, () {
            Get.toNamed(Routes.privacy);
          }),
          buildDivider(),
          buildListTile("Điểu khoản sử dụng", Icons.notifications, () {
            Get.toNamed(Routes.termOfUsers);
          }),
          buildDivider(),
          buildListTile("Thông tin phiên bản", Icons.help_outline, () {
            Get.toNamed(Routes.versionInfo);
          }),
          buildDivider(),
          buildListTile("Thông tin liên hệ", Icons.contacts, () {
            Get.toNamed(Routes.contactInfo);
          }),
          buildDivider(),
          buildListTile("Đăng xuất", Icons.logout, () {
            controller.logOut();
          }),
        ],
      ),
    );
  }

  Widget buildDivider() {
    return const Divider(
      thickness: 0.2,
      color: Colors.grey,
      indent: 15,
      endIndent: 15,
      height: 1,
    );
  }

  Widget buildListTile(String title, IconData icon, VoidCallback onTap) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 14),
      leading: Icon(
        icon,
        color: AppColors.primary,
        size: 18.sp,
      ),
      title: TextWidget(
          text: title,
          size: AppDimens.defaultText,
          fontWeight: FontWeight.w500),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 14.sp,
        color: AppColors.gray2,
      ),
      onTap: onTap,
    );
  }
}
