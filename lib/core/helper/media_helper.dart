// Helper function để xác định MediaType dựa trên phần mở rộng của file
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as path;

MediaType getMediaType(String filePath) {
  final extension = path.extension(filePath).toLowerCase();
  switch (extension) {
    // Images
    case '.jpg':
    case '.jpeg':
      return MediaType('image', 'jpeg');
    case '.png':
      return MediaType('image', 'png');
    case '.gif':
      return MediaType('image', 'gif');
    // Documents
    case '.pdf':
      return MediaType('application', 'pdf');
    case '.doc':
    case '.docx':
      return MediaType('application', 'msword');
    case '.xls':
    case '.xlsx':
      return MediaType('application', 'vnd.ms-excel');
    case '.txt':
      return MediaType('text', 'plain');
    // Videos
    case '.mp4':
      return MediaType('video', 'mp4');
    case '.mov':
      return MediaType('video', 'quicktime');
    // Default
    default:
      return MediaType('application', 'octet-stream');
  }
}
