import 'dart:convert';

import 'package:attp_2024/core/configs/contanst/prefs_contansts.dart';
import 'package:attp_2024/core/data/models/user_access_model.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';

class UserAccessUseCase {
  static final Prefs prefs = Prefs();

  static Future<void> setUserAccess(
      {required UserAccessModel userAccessModel}) async {
    List<UserAccessModel> data = [];
    try {
      String? listUserAccess = await prefs.get(PrefsConstants.listUserAccess);
      if (listUserAccess != null && listUserAccess.isNotEmpty) {
        var decodedData = jsonDecode(listUserAccess);
        if (decodedData is List) {
          data = List<UserAccessModel>.from(
            decodedData.map((item) => UserAccessModel.fromJson(item)),
          );
        } else {
          print("Stored data is not a List. Resetting user access.");
        }
      }

      final existingUserIndex =
          data.indexWhere((user) => user.userID == userAccessModel.userID);
      if (existingUserIndex != -1) {
        UserAccessModel existingUser = data.removeAt(existingUserIndex);
        data.add(existingUser);
        print("UserID already exists, moving it to the end of the list.");
      } else {
        data.add(userAccessModel);
      }

      await prefs.set(PrefsConstants.listUserAccess,
          data.map((user) => user.toJson()).toList());
    } catch (e) {
      data = [userAccessModel];
      await prefs.set(PrefsConstants.listUserAccess,
          data.map((user) => user.toJson()).toList());
      print('Error setting user access: $e');
    }
  }

  static Future<List<UserAccessModel>> getUserAccess() async {
    try {
      dynamic listUserAccess = await prefs.get(PrefsConstants.listUserAccess);
      print(listUserAccess);
      if (listUserAccess == null || listUserAccess.isEmpty) {
        return [];
      }
      var decodedData = jsonDecode(listUserAccess);
      if (decodedData is List) {
        return decodedData
            .map<UserAccessModel>((item) => UserAccessModel.fromJson(item))
            .toList();
      } else {
        print("Decoded data is not a List");
        return [];
      }
    } catch (e) {
      print('Error getting user access: $e');
      return [];
    }
  }

  static Future<void> removeUserAccessById(String userId) async {
    List<UserAccessModel> data = [];
    try {
      String? listUserAccess = await prefs.get(PrefsConstants.listUserAccess);
      if (listUserAccess != null && listUserAccess.isNotEmpty) {
        var decodedData = jsonDecode(listUserAccess);
        if (decodedData is List) {
          data = List<UserAccessModel>.from(
            decodedData.map((item) => UserAccessModel.fromJson(item)),
          );
        } else {
          print("Stored data is not a List. Cannot remove user.");
          return;
        }
      }

      data.removeWhere((user) => user.userID == userId);

      await prefs.set(PrefsConstants.listUserAccess,
          data.map((user) => user.toJson()).toList());
      print("User with ID $userId has been removed.");
    } catch (e) {
      print('Error removing user access: $e');
    }
  }
}
