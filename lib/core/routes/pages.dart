import 'package:attp_2024/features/ban_do_gia_dat_modal/di/ban_do_gia_dat_binding.dart';
import 'package:attp_2024/features/ban_do_gia_dat_modal/presentation/page/ban_do_gia_dat_page.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_ban_dau/di/cau_lao_dong_ban_dau_binding.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_ban_dau/presentation/page/cau_lao_dong_ban_dau_page.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/di/cau_lao_dong_bien_dong_binding.dart';
import 'package:attp_2024/features/cau_lao_dong/cau_lao_dong_bien_dong/presentation/page/cau_lao_dong_bien_dong_page.dart';
import 'package:get/get.dart';
import 'package:attp_2024/features/chatbot/di/chatbot_binding.dart';
import 'package:attp_2024/features/chatbot/presentation/pages/chatbot_page.dart';
import 'package:attp_2024/features/auth/dang_nhap/presentation/pages/login_page_.dart';
import 'package:attp_2024/features/expanded/CaNhan/developer/di/developer_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/developer/presentation/page/developer_page.dart';
import 'package:attp_2024/core/routes/routes.dart';
import 'package:attp_2024/features/auth/dang_nhap/di/login_binding.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/di/login_history_binding.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/presentation/pages/fingerprint_auth_page.dart';
import 'package:attp_2024/features/auth/LichSuDangNhap/presentation/pages/login_history_page.dart';
import 'package:attp_2024/features/auth/TaiKhoanNguoiDung/di/used_account_binding.dart';
import 'package:attp_2024/features/auth/TaiKhoanNguoiDung/presentation/pages/used_accounts_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/changePassword/di/changePassword_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/changePassword/presentation/page/changePassword_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/contactInfo/di/contactInfo_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/contactInfo/presentation/page/contactInfo_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/customInterface/di/customInterface_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/customInterface/presentation/page/customInterface_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/editProfile/di/editProfile_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/editProfile/presentation/page/editProfile_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/fingerPrint/di/fingerPrint_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/fingerPrint/presentation/page/fingerPrint_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/privacy/di/privacy_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/privacy/presentation/page/privacy_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/suggestion/di/suggestion_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/suggestion/presentation/page/suggestion_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/termOfUsers/di/termOfUsers_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/termOfUsers/presentation/page/termOfUsers_page.dart';
import 'package:attp_2024/features/expanded/CaNhan/versionInfo/di/versionInfo_binding.dart';
import 'package:attp_2024/features/expanded/CaNhan/versionInfo/presentation/page/versionInfo_page.dart';
import 'package:attp_2024/features/expanded/appInfo/di/appInfo_binding.dart';
import 'package:attp_2024/features/expanded/appInfo/presentation/page/appInfo_page.dart';
import 'package:attp_2024/features/main/di/main_binding.dart';
import 'package:attp_2024/features/main/presentation/pages/main_page.dart';
import 'package:attp_2024/features/nav/featureList/ThongKeList/di/ThongKeList_binding.dart';
import 'package:attp_2024/features/nav/featureList/ThongKeList/presentation/pages/ThongKeList_page.dart';
import 'package:attp_2024/features/nav/featureList/TraCuuBanDoSoList/di/TraCuuBanDoSoList_binding.dart';
import 'package:attp_2024/features/nav/featureList/TraCuuBanDoSoList/presentation/pages/TraCuuBanDoSoList_page.dart';
import 'package:attp_2024/features/nav/home/<USER>/home_binding.dart';
import 'package:attp_2024/features/nav/home/<USER>/pages/home_page.dart';
import 'package:attp_2024/features/splash/di/splash_binding.dart';
import 'package:attp_2024/features/splash/presentation/pages/splash_page.dart';
import 'package:attp_2024/features/tin_tuc/di/tinTuc_binding.dart';
import 'package:attp_2024/features/tin_tuc/presentation/page/tinTuc_page.dart';
import '../../features/nav/featureList/TraCuuList/di/TraCuuList_binding.dart';
import '../../features/nav/featureList/TraCuuList/presentation/pages/TraCuuList_page.dart';

class Pages {
  static const String initial = Routes.splash;

  static const String main = Routes.main;
  static final routes = [
    GetPage(
        name: Routes.splash,
        page: () => const SplashPage(),
        binding: SplashBinding()),
    GetPage(
        name: Routes.main,
        page: () => const MainPage(),
        binding: MainBinding()),
    GetPage(
        name: Routes.home,
        page: () => const HomePage(),
        binding: HomeBinding()),

    GetPage(
        name: Routes.login,
        page: () => const LoginPage(),
        binding: LoginBinding()),

    GetPage(
        name: Routes.loginHistory,
        page: () => const LoginFromHistoryPage(),
        binding: LoginHistoryBinding()),
    GetPage(
      name: Routes.usedAccount,
      page: () => const UsedAccountsPage(),
      binding: UsedAccountBinding(),
    ),
    GetPage(
        name: Routes.editProfile,
        page: () => const EditProfilePage(),
        binding: EditProfileBinding()),
    GetPage(
      name: Routes.changePassword,
      page: () => const ChangePasswordPage(),
      binding: ChangePasswordBinding(),
    ),
    GetPage(
      name: Routes.customInterface,
      page: () => const CustomInterfacePage(),
      binding: CustomInterfaceBinding(),
    ),
    GetPage(
      name: Routes.fingerprint,
      page: () => const FingerprintPage(),
      binding: FingerprintBinding(),
    ),
    GetPage(
      name: Routes.fingerprintAuth,
      page: () => const FingerprintAuthPage(),
      binding: LoginHistoryBinding(),
    ),

    GetPage(
      name: Routes.privacy,
      page: () => const PrivacyPage(),
      binding: PrivacyBinding(),
    ),
    GetPage(
      name: Routes.termOfUsers,
      page: () => const TermofusersPage(),
      binding: TermofusersBinding(),
    ),
    GetPage(
      name: Routes.developer,
      page: () => const DeveloperPage(),
      binding: DeveloperBinding(),
    ),
    GetPage(
      name: Routes.appInfo,
      page: () => const AppinfoPage(),
      binding: AppinfoBinding(),
    ),
    GetPage(
      name: Routes.contactInfo,
      page: () => const ContactInfoPage(),
      binding: ContactInfoBinding(),
    ),
    GetPage(
      name: Routes.versionInfo,
      page: () => const VersionInfoPage(),
      binding: VersionInfoBinding(),
    ),
    GetPage(
      name: Routes.suggestion,
      page: () => SuggestionPage(),
      binding: SuggestionBinding(),
    ),
    GetPage(
        name: Routes.tinTuc,
        page: () => const TinTucPage(),
        binding: TinTucBinding()),
    // Feature list
    GetPage(
        name: Routes.traCuuList,
        page: () => const TracuulistPage(),
        binding: TracuulistBinding()),
    GetPage(
        name: Routes.thongKeList,
        page: () => const ThongkelistPage(),
        binding: ThongkelistBinding()),
    GetPage(
        name: Routes.traCuuBanDoSoList,
        page: () => const TracuubandosolistPage(),
        binding: TracuubandosolistBinding()),
    GetPage(
        name: Routes.chatbot,
        page: () => const ChatBotPage(),
        binding: ChatBotBinding()),

    // Cầu lao động
    // Cầu lao động ban đầu
    GetPage(
        name: Routes.cauLaoDongBanDau,
        page: () => const CauLaoDongBanDauPage(),
        binding: CauLaoDongBanDauBinding()),
    // Cầu lao động biến động
    GetPage(
        name: Routes.cauLaoDongBienDong,
        page: () => const CauLaoDongBienDongPage(),
        binding: CauLaoDongBienDongBinding()),
    GetPage(
        name: Routes.banDoGiaDatModal,
        page: () => const BanDoGiaDatModalPage(),
        binding: BanDoGiaDatModalBinding()),
  ];
}
