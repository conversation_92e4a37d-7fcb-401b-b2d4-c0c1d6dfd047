import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/labor_demand_response_model.dart';
import 'package:attp_2024/core/data/models/result.dart';

class LaborDemandData extends CoreService {
  Future<Result<List<LaborDemandResponseModel>>> fetchAllLaborDemand(
      {required ProcRequestModel allLabordemandRequest}) async {
    final jsonBody = allLabordemandRequest.toJson();

    try {
      var response = await dioService.dio.post(
        EndpointConfig.procEndPoint(proc: ProcConstants.getAllLaborDemand),
        data: jsonBody,
      );

      if (response.statusCode == 200) {
        List<LaborDemandResponseModel> dataResponse = [];
        if (response.data["data"] != null) {
          if (response.data["data"] is List) {
            dataResponse = (response.data["data"] as List)
                .map((item) => LaborDemandResponseModel.fromJson(item))
                .toList();
          }
        }
        print(dataResponse[0].toJson());
        return Result.success(dataResponse);
      } else {
        return Result.error(ApiError.badRequest);
      }
    } catch (e) {
      print("Exception occurred: $e");
      return Result.error(ApiError.badRequest);
    }
  }
}
