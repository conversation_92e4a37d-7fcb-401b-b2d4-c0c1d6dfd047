import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/DuLieuMauNguoiLaoDongResponseModel.dart';
import 'package:attp_2024/core/data/models/result.dart';

class DuLieuMauNguoiLaoDongData extends CoreService {
  // Fetch dữ liệu loại hình
  Future<Result<List<DuLieuMauNguoiLaoDongResponseModel>>>
      fetchDuLieuMauNguoiLaoDong({required ProcRequestModel request}) {
    final body = request.toJson();
    return generateData<DuLieuMauNguoiLaoDongResponseModel>(
      proc: ProcConstants.getMauDuLieuNguoiLaoDong,
      fromJson: DuLieuMauNguoiLaoDongResponseModel.fromJson,
      request: body,
    );
  }
}
