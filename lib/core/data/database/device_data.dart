// ignore: file_names
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/device_response_model.dart';
import 'package:attp_2024/core/data/models/result.dart';

class DeviceData extends CoreService {
  Future<Result<List<DeviceResponseModel>>> addDevice(
      {required ProcRequestModel request}) {
    final body = request.toJson();
    return generateData<DeviceResponseModel>(
      proc: ProcConstants.addDevice,
      fromJson: DeviceResponseModel.fromJson,
      request: body,
    );
  }

  Future<Result<List<DeviceResponseModel>>> updateStateDevice(
      {required ProcRequestModel request}) {
    final body = request.toJson();
    return generateData<DeviceResponseModel>(
      proc: ProcConstants.updateDevice,
      fromJson: DeviceResponseModel.fromJson,
      request: body,
    );
  }

  Future<Result<List<DeviceResponseModel>>> getListDevice(
      {required ProcRequestModel request}) async {
    final body = request.toJson();
    return generateData<DeviceResponseModel>(
      proc: ProcConstants.getAllDevice,
      fromJson: DeviceResponseModel.fromJson,
      request: body,
    );
  }
}
