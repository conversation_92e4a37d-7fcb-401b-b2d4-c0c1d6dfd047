// ignore: file_names
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/MauNhuCauTuyenDungResponseModel.dart';
import 'package:attp_2024/core/data/models/result.dart';

class MauNhuCauTuyenDungData extends CoreService {
  Future<Result<List<MauNhuCauTuyenDungResponseModel>>> fetchMauNhuCauTuyenDung(
      {required ProcRequestModel request}) {
    final body = request.toJson();
    return generateData<MauNhuCauTuyenDungResponseModel>(
      proc: ProcConstants.getMauNhuCauTuyenDung,
      fromJson: MauNhuCauTuyenDungResponseModel.fromJson,
      request: body,
    );
  }
}
