import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/data_base_response_model.dart';
import 'package:attp_2024/core/data/models/result.dart';

class DataBaseData extends CoreService {
  // Fetch dữ liệu loại hình
  Future<Result<List<DataBaseResponseModel>>> fetchDuLieuMauNguoiLaoDong(
      {required ProcRequestModel request}) {
    final body = request.toJson();
    return generateData<DataBaseResponseModel>(
      proc: ProcConstants.getMauDuLieuNguoiLaoDong,
      fromJson: DataBaseResponseModel.fromJson,
      request: body,
    );
  }
}
