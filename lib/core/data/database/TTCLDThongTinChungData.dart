// ignore: file_names
import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/CauLaoDongDeleteReponseModel.dart';
import 'package:attp_2024/core/data/dto/response/CauLaoDongModel.dart';
import 'package:attp_2024/core/data/dto/response/cauLaoDongResponse.dart';
import 'package:attp_2024/core/data/models/result.dart';

class TTCLDThongTinChungData extends CoreService {
  Future<Result<List<CauLaoDongModel>>> luuThongTinCauLaoDong(
      {required List<dynamic> request}) {
    return generateData<CauLaoDongModel>(
      proc: ProcConstants.luuThongTinCauLaoDong,
      fromJson: CauLaoDongModel.fromJson,
      request: request,
    );
  }

  Future<Result<List<CauLaoDongResponse>>> layCauLaoDongById(
      {required List<dynamic> request}) {
    return generateData<CauLaoDongResponse>(
      proc: ProcConstants.layCauLaoDongById,
      fromJson: CauLaoDongResponse.fromJson,
      request: request,
    );
  }

  Future<Result<List<CauLaoDongDeleteResponseModel>>> xoaThongTinCauLaoDong(
      {required ProcRequestModel request}) {
    return generateData<CauLaoDongDeleteResponseModel>(
      proc: ProcConstants.xoaThongTinCauLaoDong,
      fromJson: CauLaoDongDeleteResponseModel.fromJson,
      request: request.toJson(),
    );
  }
}
