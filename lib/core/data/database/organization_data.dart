import 'package:attp_2024/core/configs/contanst/proc_constants.dart';
import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/proc_request_model.dart';
import 'package:attp_2024/core/data/dto/response/organization_response_model.dart';
import 'package:attp_2024/core/data/models/result.dart';

import '../dto/request/post_organization_directory_model.dart';

class OrganizationData extends CoreService {
  Future<Result<List<OrganizationResponseModel>>> fetchAllToChuc(
      {required ProcRequestModel allToChucRequest}) {
    final jsonBody = allToChucRequest.toJson();
    return postData<List<OrganizationResponseModel>>(
      endpoint: EndpointConfig.procEndPoint(proc: ProcConstants.getAllToChuc),
      data: jsonBody,
      parse: (data) {
        if (data != null && data is List) {
          return (data)
              .map((item) => OrganizationResponseModel.fromJson(item))
              .toList();
        }
        return [];
      },
    );
  }

  Future<Result<List<OrganizationResponseModel>>> postToChuc(
      {required PostOrganizationRequest postToChucRequest}) {
    final jsonBody = postToChucRequest.toJson();
    return postData<List<OrganizationResponseModel>>(
      endpoint: EndpointConfig.procEndPoint(proc: ProcConstants.postToChuc),
      data: jsonBody,
      parse: (data) {
        if (data != null && data is List) {
          return (data)
              .map((item) => OrganizationResponseModel.fromJson(item))
              .toList();
        }
        return [];
      },
    );
  }
}
