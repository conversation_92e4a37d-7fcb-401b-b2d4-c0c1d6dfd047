import 'dart:developer';
import 'package:attp_2024/core/helper/media_helper.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;
import 'package:attp_2024/core/services/user_use_case.dart';
import 'package:dio/dio.dart' hide Response, MultipartFile, FormData;
import 'package:dio/dio.dart' as dio show Response, MultipartFile, FormData;
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:uuid/uuid.dart';

class UploadService extends CoreService {
  final Uuid _uuid = const Uuid();

  // Helper function để tạo tên file với UUID
  String _generateUuidFileName(String originalPath) {
    final extension = path.extension(originalPath);
    final uuid = _uuid.v4(); // Tạo UUID v4
    return '$uuid$extension';
  }

  // ignore: non_constant_identifier_names
  Future<String> _get_token() async {
    final userId = await UserUseCase.getUser();
    return userId!.token;
  }

  // ignore: non_constant_identifier_names
  Future<String> _get_donviCode() async {
    final userId = await UserUseCase.getUser();
    return userId!.donViID;
  }

  // ignore: non_constant_identifier_names
  Future<String> _get_siteUrl() async {
    final userId = await UserUseCase.getUser();
    return userId!.siteURL;
  }

// Hàm gửi 1 file từ file đính kèm lên API (không bao gồm video)
  //(Sau này sẽ có hàm gửi dữ liệu hình ảnh + video lên API, vui lòng hong chỉnh sửa - chỉnh kau quánh bòm đầu)
  Future<dio.Response?> uploadFile(
      {required String filePath,
      required String loaiVB,
      required String formName}) async {
    try {
      // Kiểm tra file MP4
      if (path.extension(filePath).toLowerCase() == '.mp4' ||
          path.extension(filePath).toLowerCase() == '.mov') {
        SnackbarUtil.showError("Không hỗ trợ upload file video",
            alignment: "bottom");
        return null;
      }
      String token = await _get_token();
      String donviID = await _get_donviCode();
      String siteUrl = await _get_siteUrl();
      String url =
          '${siteUrl}api/UploadMultiFiles?loaiVB=$loaiVB&donViCode=$donviID&formName=$formName';
      dio.FormData formData = dio.FormData();
      String uuidFileName = _generateUuidFileName(filePath);
      formData.files.add(
        MapEntry(
          'files',
          await dio.MultipartFile.fromFile(
            filePath,
            filename: uuidFileName,
            contentType: getMediaType(filePath),
          ),
        ),
      );
      // Gọi API upload
      dio.Response response = await dioService.dio.post(
        url,
        data: formData,
        options: Options(
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "multipart/form-data",
          },
        ),
      );
      if (response.statusCode == 200) {
        log("Tải lên thành công: ${response.data}", name: "UploadService");
        return response;
      } else {
        log("Tải lên thất bại: ${response.statusCode} - ${response.data}",
            name: "UploadService");
        return response;
      }
    } catch (e) {
      log("Lỗi khi tải lên: $e");
      return null;
    }
  }

  // Hàm gửi nhiều dữ liệu từ file đính kèm lên API (không bao gồm video)
  //(Sau này sẽ có hàm gửi dữ liệu hình ảnh + video lên API, vui lòng hong chỉnh sửa - chỉnh kau quánh bòm đầu)
  Future<dio.Response?> uploadMultiFiles(
      {required List<String> filePaths,
      required String loaiVB,
      required String formName}) async {
    try {
      // Kiểm tra file MP4
      for (String filePath in filePaths) {
        if (path.extension(filePath).toLowerCase() == '.mp4' ||
            path.extension(filePath).toLowerCase() == '.mov') {
          SnackbarUtil.showError("Không hỗ trợ upload file video",
              alignment: "bottom");
          return null;
        }
      }
      String token = await _get_token();
      String donviID = await _get_donviCode();
      String siteUrl = await _get_siteUrl();
      String url =
          '${siteUrl}api/UploadMultiFiles?loaiVB=$loaiVB&donViCode=$donviID&formName=$formName';
      dio.FormData formData = dio.FormData();
      for (String filePath in filePaths) {
        String uuidFileName = _generateUuidFileName(filePath);
        formData.files.add(
          MapEntry(
            'files',
            await dio.MultipartFile.fromFile(
              filePath,
              filename: uuidFileName,
              contentType: getMediaType(filePath),
            ),
          ),
        );
      }
      // Gọi API upload
      dio.Response response = await dioService.dio.post(
        url,
        data: formData,
        options: Options(
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "multipart/form-data",
          },
        ),
      );
      if (response.statusCode == 200) {
        log("Tải lên thành công: ${response.data}", name: "UploadService");
        return response;
      } else {
        log("Tải lên thất bại: ${response.statusCode} - ${response.data}",
            name: "UploadService");
        return response;
      }
    } catch (e) {
      log("Lỗi khi tải lên: $e");
      return null;
    }
  }
}
