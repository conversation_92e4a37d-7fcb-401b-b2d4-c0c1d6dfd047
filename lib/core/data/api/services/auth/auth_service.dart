import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:attp_2024/core/configs/contanst/prefs_contansts.dart';
import 'package:attp_2024/core/configs/enum.dart';
import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';
import 'package:attp_2024/core/data/dto/request/login_request_model.dart';
import 'package:attp_2024/core/data/dto/response/login_response_model.dart';
import 'package:attp_2024/core/data/models/database/database_model.dart';
import 'package:attp_2024/core/data/models/result.dart';
import 'package:attp_2024/core/data/prefs/prefs.dart';
import '../../../dto/response/permission_response.dart';

//KẾ THỪA METHOD DÙNG CHUNG TRONG API SERVICE
class AuthService extends CoreService {
  // LẤY THÔNG TIN MÁY CHỦ
  Future<Result<List<DatabaseModel>>> fetchDatabase(String appName) async {
    try {
      // Make a GET request without a token
      final response = await dioService.dioPublic.get(
        EndpointConfig.getDataBaseEndpoint(appName: appName),
        options: Options(headers: {
          "Content-Type": "application/json", // Explicit header
        }),
      );

      if (response.statusCode == 200) {
        // Parse the response data
        List<dynamic> rawData = jsonDecode(response.data['data']);
        return Result.success(
            rawData.map((item) => DatabaseModel.fromJson(item)).toList());
      } else {
        throw Exception("Failed to fetch database information");
      }
    } catch (e) {
      throw Exception("Error fetching database: $e");
    }
  }

  // LẤY ID MÁY CHỦ
  Future<List<String>> fetchIdDatabase(String appName) async {
    try {
      final response = await dioService.dioPublic
          .get(EndpointConfig.getDataBaseEndpoint(appName: appName));
      if (response.data['success']) {
        final List<dynamic> rawData = jsonDecode(response.data['data']);
        return rawData
            .where((item) => item['DBData_Name'] == DATABASE_NAME)
            .map<String>((item) => item['ID'])
            .toList();
      } else {
        throw Exception("Failed to fetch data");
      }
    } catch (e) {
      throw Exception("Error fetching database: $e");
    }
  }

  // ĐĂNG NHẬP
  Future<Result<LoginResponseModel>> login(
      {required LoginRequestModel loginRequestModel}) async {
    try {
      final response = await dioService.dioPublic
          .post(EndpointConfig.loginEndpoint, data: loginRequestModel.toJson());
      return Result.success(
          LoginResponseModel.fromJson(response.data["data"][0]));
    } catch (e) {
      return Result.error(ApiError.badRequest);
    }
  }

  // PHÂN QUYỀN
  Future<Result<List<PermissionResponseModel>>> permission(String id) async {
    try {
      final response = await dioService.dio.post(
        EndpointConfig.permissonEndpoint + id,
        data: [],
      );
      if (response.statusCode == 200 && response.data["data"] != null) {
        // Parse the list of permissions from the response data
        List<PermissionResponseModel> permissions =
            (response.data["data"] as List)
                .map((item) => PermissionResponseModel.fromJson(item))
                .toList();

        return Result.success(permissions);
      } else {
        return Result.error(ApiError.badRequest);
      }
    } catch (e) {
      print('Error fetching permissions: $e');
      return Result.error(ApiError.badRequest);
    }
  }

  // ĐĂNG XUẤT
  Future<void> logout() async {
    try {
      await Prefs.preferences.remove(PrefsConstants.tokenKey);
    } catch (e) {
      print("Error during logout: $e");
    }
  }
}
