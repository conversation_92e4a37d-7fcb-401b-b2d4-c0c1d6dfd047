import 'dart:developer';

import 'package:attp_2024/core/data/api/configs/endpoint_config.dart';
import 'package:attp_2024/core/data/api/services/core_service.dart';

/// Class để gọi API kế thừa từ CoreService
/// (lib\core\data\api\services\core_service.dart)
/// Tham số api là tên API được gọi
/// Tham số body là tham số để được gửi lên server
///
class APIService extends CoreService {
  Future<dynamic> callAPI(String api, [dynamic body = const []]) async {
    try {
      final response = await dioService.dio.post(
        EndpointConfig.apiEndPoint(api: api),
        data: body,
      );
      log(response.toString(), name: 'APIService.callAPI.response');
      if (response.statusCode == 401) {
        throw Exception("Unauthorized: Token is invalid or expired");
      }
      final data = response.data;
      if (data == null) {
        throw Exception("No response data received from the server");
      }
      log(data.toString(), name: 'APIService.callAPI.data');
      return data;
    } catch (e) {
      log("Error calling procedure: $e", name: 'akr');
      throw Exception("Error calling procedure: $e");
    }
  }
}
