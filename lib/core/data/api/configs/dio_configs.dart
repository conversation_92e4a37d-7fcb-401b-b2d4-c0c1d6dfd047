
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_disposable.dart';
import 'package:logger/logger.dart';
import '../../../services/auth_use_case/auth_use_case.dart';

class DioService extends GetxService {
  late Dio _dio;
  late Dio _dioPublic;
  var logger = Logger();

  Future<DioService> init() async {
    final String baseUrl =
        dotenv.env['BASE_URL'] ?? (dotenv.env['BACKUP_URL'] ?? "");
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 15),
      headers: {
        "Content-Type": "application/json",
      },
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        try {
          final token = await AuthUseCase.getTokenMemory();
          if (token.isNotEmpty) {
            options.headers['Authorization'] =
                'Bearer ${token.replaceAll('"', '')}';
          }
          return handler.next(options);
        } catch (e) {
          logger.w(e);
        }
      },
      onResponse: (response, handler) {
        return handler.next(response);
      },
      onError: (DioException error, handler) {
        if (error.response?.statusCode == 401) {
          logger.w(error);
        }
        return handler.next(error);
      },
    ));

    _dioPublic = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 15),
      headers: {
        "Content-Type": "application/json",
      },
    ));

    return this;
  }

  Dio get dio => _dio;

  Dio get dioPublic => _dioPublic;
}
