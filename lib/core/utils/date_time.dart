import 'package:intl/intl.dart';

class DateTimeUtil {
  static String formatDateTimeFormat(DateTime dateTime) {
    String formattedDate = DateFormat('d MMMM, EEEE').format(dateTime);
    return formattedDate;
  }

  static String format(DateTime dateTime) {
    String formattedDate = DateFormat("dd/MM/yyyy'T'00:00:00").format(dateTime);
    return formattedDate;
  }

  static DateTime? toDatetime(String dateTime) {
    try {
      return DateFormat('dd/MM/yyyy')
          .parse(dateTime); // Chuyển đổi từ định dạng dd/MM/yyyy
    } catch (e) {
      return null; // Trả về null nếu có lỗi
    }
  }

  static DateTime getCurrentDate() {
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('dd/MM/yyyy').format(now);
    return DateFormat('dd/MM/yyyy').parse(formattedDate);
  }

  static String formatCustom(DateTime? dateTime) {
    if (dateTime != null) {
      DateFormat formatter = DateFormat('dd/MM/yyyy');
      return formatter.format(dateTime);
    } else {
      DateTime now = DateTime.now(); // Lấy thời gian hiện tại
      DateFormat formatter = DateFormat('dd/MM/yyyy');
      return formatter.format(now);
    }
  }
}
