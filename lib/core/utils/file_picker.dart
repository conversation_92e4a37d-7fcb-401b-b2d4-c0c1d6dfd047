import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';



Future<void> pickImagesAndVideos({
  required Function(List<String>) onFilesPicked,
  int maxFiles = 3, // Giới hạn số lượng tệp tối đa
}) async {
  try {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: ['jpg', 'png', 'mp4'],
    );

    if (result != null && result.files.isNotEmpty) {
      final filePaths = result.files.map((file) => file.path!).toList();

      if (filePaths.length > maxFiles) {
        // Nếu số lượng tệp vượt quá giới hạn, chỉ chọn số tệp cho phép
        print('Bạn chỉ có thể chọn tối đa $maxFiles tệp.');
        onFilesPicked(
            filePaths.take(maxFiles).toList()); // Trả về danh sách tệp giới hạn
      } else {
        onFilesPicked(filePaths);
      }
    } else {
      print('Không có tệp nào được chọn');
    }
  } catch (e) {
    print('Lỗi khi chọn tệp: $e');
  }
}

Future<void> pickImagesAndPdf({
  required Function(List<String>) onFilesPicked,
  int maxFiles = 3, // Giới hạn số lượng tệp tối đa
}) async {
  try {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: ['jpg', 'png', 'pdf'],
    );

    if (result != null && result.files.isNotEmpty) {
      final filePaths = result.files.map((file) => file.path!).toList();

      if (filePaths.length > maxFiles) {
        // Nếu số lượng tệp vượt quá giới hạn, chỉ chọn số tệp cho phép
        SnackbarUtil.showWarning('Bạn chỉ được chọn tối đa $maxFiles tệp.');
        onFilesPicked(
            filePaths.take(maxFiles).toList()); // Trả về danh sách tệp giới hạn
      } else {
        onFilesPicked(filePaths);
      }
    } else {
      print('Không có tệp nào được chọn');
    }
  } catch (e) {
    print('Lỗi khi chọn tệp: $e');
  }
}

Future<void> pickImages({
  required Function(List<String>) onFilesPicked,
  int maxFiles = 3, // Giới hạn số lượng tệp tối đa
}) async {
  try {
    final ImagePicker picker = ImagePicker();
    final List<XFile>? pickedFiles = await picker.pickMultiImage();

    if (pickedFiles != null && pickedFiles.isNotEmpty) {
      final filePaths = pickedFiles.map((file) => file.path).toList();

      if (filePaths.length > maxFiles) {
        print('Bạn chỉ có thể chọn tối đa $maxFiles tệp.');
        onFilesPicked(filePaths.take(maxFiles).toList());
      } else {
        onFilesPicked(filePaths);
      }
    } else {
      print('Không có tệp nào được chọn');
    }
  } catch (e) {
    print('Lỗi khi chọn tệp: $e');
  }
}

Future<List<XFile>?> pickImagesAndVideos2() async {
  final ImagePicker picker = ImagePicker();
  List<XFile> selectedMedia = [];

  try {
    final List<XFile>? images = await picker.pickMultiImage(); // Chọn nhiều ảnh
    if (images != null && images.isNotEmpty) {
      selectedMedia.addAll(images);
    }

    final XFile? video = await picker.pickVideo(
        source: ImageSource.gallery); // Chọn một video từ thư viện
    if (video != null) {
      selectedMedia.add(video);
    }

    if (selectedMedia.isEmpty) {
      return null; // Trả về null nếu không có gì được chọn
    }

    return selectedMedia; // Trả về danh sách các XFile (ảnh và video)
  } catch (e) {
    print("Lỗi khi chọn ảnh và video: $e");
    // Xử lý lỗi nếu cần thiết, ví dụ: hiển thị thông báo lỗi cho người dùng
    return null; // Trả về null trong trường hợp lỗi
  }
}

Future<void> pickMedia({
  required Function(List<String>) onFilesPicked,
  int maxFiles = 3, // Giới hạn số lượng tệp tối đa
  bool allowVideos = false, // Cho phép chọn video nếu bật
}) async {
  try {
    final ImagePicker picker = ImagePicker();
    List<XFile> pickedFiles = [];

    // Chọn hình ảnh
    final List<XFile>? pickedImages = await picker.pickMultiImage();
    if (pickedImages != null) {
      pickedFiles.addAll(pickedImages);
    }

    // Chọn video nếu được phép
    if (allowVideos) {
      final XFile? pickedVideo =
          await picker.pickVideo(source: ImageSource.gallery);
      if (pickedVideo != null) {
        pickedFiles.add(pickedVideo);
      }
    }

    if (pickedFiles.isNotEmpty) {
      final filePaths = pickedFiles.map((file) => file.path).toList();

      // Giới hạn số lượng file
      if (filePaths.length > maxFiles) {
        print('Bạn chỉ có thể chọn tối đa $maxFiles tệp.');
        onFilesPicked(filePaths.take(maxFiles).toList());
      } else {
        onFilesPicked(filePaths);
      }
    } else {
      print('Không có tệp nào được chọn');
    }
  } catch (e) {
    print('Lỗi khi chọn tệp: $e');
  }
}
