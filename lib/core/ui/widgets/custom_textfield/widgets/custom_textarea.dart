import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

import '../../../../configs/dimens/app_dimens.dart';

class TextAreaWidget extends StatefulWidget {
  final String title;
  final bool hideTitle;
  final double height;
  final TextStyle? titleStyle;
  final bool isRequired;
  final String placeholder;
  final double cornerRadius;
  final bool isDisabled;
  final String value;
  final String? warningText;
  final bool showClearButton;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Function(String) onChange;
  final Function()? onTap;
  final FocusNode? focusNode;
  final String? errorWidget;
  final double? maxWidth;

  const TextAreaWidget({
    Key? key,
    required this.title,
    this.hideTitle = false,
    this.height = 200,
    this.titleStyle,
    this.isRequired = false,
    required String initialValue,
    required this.placeholder,
    this.cornerRadius = 10.0,
    this.isDisabled = false,
    this.warningText,
    this.showClearButton = true,
    this.prefixIcon,
    this.suffixIcon,
    required this.onChange,
    this.onTap,
    this.focusNode,
    this.errorWidget,
    this.maxWidth,
  })  : value = initialValue,
        super(key: key);

  @override
  _TextAreaWidgetState createState() => _TextAreaWidgetState();
}

class _TextAreaWidgetState extends State<TextAreaWidget> {
  late TextEditingController _controller;
  final RxBool showError = false.obs;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!widget.hideTitle)
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: widget.title,
                  style: widget.titleStyle?.copyWith(
                        color: Colors.black,
                        fontSize: AppDimens.sizeTitle,
                        fontWeight: FontWeight.w500,
                      ) ??
                      const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppColors.black,
                        fontSize: AppDimens.sizeTitle,
                      ),
                ),
                if (widget.isRequired)
                  const TextSpan(
                    text: ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: AppDimens.textSize14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        const SizedBox(height: 4.0),
        Obx(() {
          final isError = showError.value;
          return SizedBox(
            width: widget.maxWidth,
            child: TextField(
              enabled: !widget.isDisabled,
              controller: _controller,
              maxLines: 7,
              onChanged: (value) {
                showError.value = widget.isRequired && value.isEmpty;
                widget.onChange(value);
                setState(() {});
              },
              decoration: InputDecoration(
                hintText: widget.placeholder,
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                hintStyle: TextStyle(
                  color: widget.isDisabled
                      ? AppColors.borderInputDisabled
                      : AppColors.borderInputDisabled,
                  fontSize: AppDimens.textSize14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: BorderSide(
                    color: isError ? Colors.red : Colors.grey,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: BorderSide(
                    color: (widget.errorWidget?.isNotEmpty ?? false)
                        ? Colors.red
                        : AppColors.focusColor,
                    width: 1,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: BorderSide(
                    color: (widget.errorWidget?.isNotEmpty ?? false)
                        ? Colors.red
                        : AppColors.borderInput1,
                    width: .3,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.cornerRadius),
                  borderSide: const BorderSide(
                    color: AppColors.borderInputDisabled,
                    width: .3,
                  ),
                ),
                filled: true,
                fillColor:
                    widget.isDisabled ? Colors.grey.shade200 : Colors.white,
                errorText: isError ? widget.warningText : null,
                errorStyle: const TextStyle(color: Colors.red, fontSize: 12),
                prefixIcon: widget.prefixIcon,
                suffixIcon: Padding(
                  padding: const EdgeInsets.only(bottom: 140),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.suffixIcon != null) widget.suffixIcon!,
                      if (widget.showClearButton && _controller.text.isNotEmpty)
                        IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _controller.clear();
                            widget.onChange('');
                          },
                        ),
                    ],
                  ),
                ),
              ),
              style: TextStyle(
                color: widget.isDisabled ? Colors.grey.shade600 : Colors.black,
              ),
              cursorColor: widget.isDisabled ? Colors.grey : Colors.black,
            ),
          );
        }),
        widget.errorWidget != null
            ? Text(
                widget.errorWidget!,
                style:
                    TextStyle(color: Colors.red, fontSize: AppDimens.smallText),
              )
            : const SizedBox.shrink(),
      ],
    );
  }
}
