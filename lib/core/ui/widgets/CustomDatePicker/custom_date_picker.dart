import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';

class CustomDatePicker extends StatefulWidget {
  final String title;
  final bool hideTitle;
  final TextStyle? titleStyle;
  final bool isRequired;
  final String placeholder;
  final double cornerRadius;
  final bool isDisabled;
  final bool isTyping;
  final DateTime? date;
  final String? warningText;
  final bool showClearButton;
  final Function(DateTime?) onChange;
  final String? errorWidget;
  final int? widthSize;
  final double? heightTextField;
  final DateTime? setTime; // Đặt kiểu DateTime
  final bool isMaximumDate;
  final Widget? prefixIcon;

  const CustomDatePicker(
      {super.key,
      required this.title,
      this.hideTitle = false,
      this.titleStyle,
      this.isRequired = false,
      required this.placeholder,
      this.cornerRadius = 5.0,
      this.isDisabled = false,
      this.isTyping = false,
      this.date,
      this.widthSize,
      this.warningText,
      this.showClearButton = true,
      required this.onChange,
      this.errorWidget,
      this.heightTextField = 45,
      this.setTime, // Truyền giá trị mới khi cần
      this.isMaximumDate = false,
      this.prefixIcon});

  @override
  // ignore: library_private_types_in_public_api
  _CustomDatePickerState createState() => _CustomDatePickerState();
}

class _CustomDatePickerState extends State<CustomDatePicker> {
  final TextEditingController _dateController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _showError = false;
  bool _showWarningText = false;

  @override
  void initState() {
    super.initState();
    _updateDateController(); // Khởi tạo giá trị cho _dateController
  }

  // Phương thức cập nhật giá trị cho _dateController
  void _updateDateController() {
    if (widget.setTime != null) {
      _dateController.text =
          DateFormat('dd/MM/yyyy', 'vi_VN').format(widget.setTime!);
    } else if (widget.date != null) {
      _dateController.text =
          DateFormat('dd/MM/yyyy', 'vi_VN').format(widget.date!);
    } else {
      _dateController.clear(); // Xóa nếu không có giá trị nào
    }
  }

  @override
  void didUpdateWidget(CustomDatePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Nếu setTime thay đổi, cập nhật lại _dateController
    if (oldWidget.setTime != widget.setTime) {
      _updateDateController();
    }
  }

  void _showCupertinoDatePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(0),
          topRight: Radius.circular(0),
        ),
      ),
      builder: (BuildContext builder) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.25,
          child: Transform.scale(
            scale: 0.8,
            child: CupertinoDatePicker(
              initialDateTime:
                  widget.setTime ?? DateTime.now(), // Sử dụng setTime
              mode: CupertinoDatePickerMode.date,
              maximumDate: widget.isMaximumDate ? DateTime.now() : null,
              onDateTimeChanged: (DateTime newDate) {
                widget.onChange(newDate);
                setState(() {
                  _dateController.text =
                      DateFormat('dd/MM/yyyy', 'vi_VN').format(newDate);
                  _showError = false;
                  _showWarningText = false;
                });
              },
            ),
          ),
        );
      },
    );
  }

  void _validateInput() {
    if (widget.isRequired && _dateController.text.isEmpty) {
      setState(() {
        _showError = true;
        _showWarningText = true;
      });
      _focusNode.requestFocus();
    } else {
      setState(() {
        _showError = false;
        _showWarningText = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!widget.hideTitle)
          Row(
            children: [
              if (!widget.hideTitle)
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: widget.title,
                        style: widget.titleStyle?.copyWith(
                              color: AppColors.black,
                              fontSize: AppDimens.textSize17,
                              fontWeight: FontWeight.normal,
                            ) ??
                            const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: AppColors.black,
                              fontSize: 15,
                            ),
                      ),
                      if (widget.isRequired)
                        const TextSpan(
                          text: ' *',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                ),
            ],
          ),
        const SizedBox(height: 4.0),
        SizedBox(
          height: widget.heightTextField,
          width: widget.widthSize?.w,
          child: TextField(
            controller: _dateController,
            focusNode: _focusNode,
            enabled: !widget.isDisabled,
            readOnly: !widget.isTyping,
            decoration: InputDecoration(
              prefixIcon: widget.prefixIcon,
              hintText: widget.placeholder,
              hintStyle: const TextStyle(
                color: Colors.grey,
                fontSize: AppDimens.textSize13,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.cornerRadius),
                borderSide: BorderSide(
                  color: _showError ? AppColors.error : AppColors.borderInput1,
                  width: .5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.cornerRadius),
                borderSide: BorderSide(
                  color: _showError ? Colors.red : AppColors.borderInput1,
                  width: .3,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(widget.cornerRadius),
                borderSide: BorderSide(
                  color: _showError ? AppColors.error : AppColors.borderInput1,
                  width: .5,
                ),
              ),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _dateController.text.isNotEmpty && widget.showClearButton
                      ? InkWell(
                          onTap: () {
                            widget.onChange(null);
                            _dateController.clear();
                            _validateInput();
                          },
                          child: const Icon(
                            CupertinoIcons.clear_circled_solid,
                            color: AppColors.iconColors,
                            size: 18,
                          ),
                        )
                      : const SizedBox(
                          width: 18,
                        ),
                  const SizedBox(width: 3),
                  InkWell(
                    onTap: () {
                      if (!widget.isDisabled) {
                        _showCupertinoDatePicker(context);
                      }
                    },
                    child: const Icon(
                      CupertinoIcons.calendar,
                      color: Colors.black,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
            style: const TextStyle(
                fontSize: AppDimens.textSize15, color: CupertinoColors.black),
            onTap: widget.isDisabled
                ? null
                : () {
                    if (!widget.isTyping) {
                      _showCupertinoDatePicker(context);
                    }
                  },
            onEditingComplete: _validateInput,
          ),
        ),
        if (_showWarningText && widget.warningText != null)
          Padding(
            padding: const EdgeInsets.only(top: 0.0),
            child: Text(
              widget.warningText!,
              style: const TextStyle(
                  color: AppColors.error, fontSize: AppDimens.textSize12),
            ),
          ),
        if (widget.errorWidget != null)
          Text(
            widget.errorWidget!,
            style: const TextStyle(color: AppColors.error),
          ),
        const SizedBox(height: 20),
      ],
    );
  }
}
