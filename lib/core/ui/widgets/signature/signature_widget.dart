import 'dart:developer';
import 'dart:typed_data';
import 'dart:io';
import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/snackbar/snackbar_until.dart';
import 'package:attp_2024/core/ui/widgets/button/button_widget.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:signature/signature.dart';
import 'package:path_provider/path_provider.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart' as permission;
import 'package:get/get.dart';

class SignatureWidget extends StatefulWidget {
  final Function(String)? onSaved;
  final String? errorText;

  const SignatureWidget({super.key, this.onSaved, this.errorText});

  @override
  _SignatureWidgetState createState() => _SignatureWidgetState();
}

class _SignatureWidgetState extends State<SignatureWidget> {
  final SignatureController _controller = SignatureController(
    penStrokeWidth: 2.0,
    penColor: Colors.black,
    exportBackgroundColor: Colors.transparent,
  );
  final ImagePicker _picker = ImagePicker();
  List<List<Point>> _previousPoints = [];
  File? _loadedImage;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<String?> _getTempSignaturePath() async {
    try {
      if (_controller.isNotEmpty || _loadedImage != null) {
        if (_controller.isNotEmpty) {
          final Uint8List? signatureData = await _controller.toPngBytes();
          if (signatureData != null) {
            final directory = await getTemporaryDirectory();
            final String fileName =
                "signature_${DateTime.now().millisecondsSinceEpoch}.png";
            final String tempPath = '${directory.path}/$fileName';
            final File tempFile = File(tempPath);
            await tempFile.writeAsBytes(signatureData);
            return tempPath;
          }
        } else if (_loadedImage != null) {
          return _loadedImage!.path;
        }
      }
      return null;
    } catch (e) {
      log("Lỗi khi lấy đường dẫn tạm thời: $e");
      return null;
    }
  }

  Future<void> _onDrawEnd() async {
    final String? tempPath = await _getTempSignaturePath();
    if (tempPath != null) {
      widget.onSaved?.call(tempPath);
    }
  }

  Future<void> _saveSignature() async {
    try {
      if (_controller.isNotEmpty || _loadedImage != null) {
        // Kiểm tra quyền truy cập
        final permitted = await PhotoManager.requestPermissionExtend();
        if (!permitted.isAuth) {
          SnackbarUtil.showError("Cần cấp quyền truy cập để lưu ảnh",
              alignment: "bottom");
          return;
        }

        String? savedPath;
        if (_controller.isNotEmpty) {
          final Uint8List? signatureData = await _controller.toPngBytes();
          if (signatureData == null) {
            SnackbarUtil.showWarning('Không có dữ liệu chữ ký để lưu',
                alignment: "bottom");
            return;
          }

          final String fileName =
              "signature_${DateTime.now().millisecondsSinceEpoch}.png";
          final AssetEntity? asset = await PhotoManager.editor.saveImage(
            signatureData,
            title: fileName,
            filename: fileName,
          );

          if (asset != null) {
            final File? savedFile = await asset.file;
            if (savedFile != null) {
              savedPath = savedFile.path;
            }
          }
        } else if (_loadedImage != null) {
          final bytes = await _loadedImage!.readAsBytes();
          final String fileName =
              "signature_${DateTime.now().millisecondsSinceEpoch}.png";
          final AssetEntity? asset = await PhotoManager.editor.saveImage(
            bytes,
            title: fileName,
            filename: fileName,
          );

          if (asset != null) {
            final File? savedFile = await asset.file;
            if (savedFile != null) {
              savedPath = savedFile.path;
            }
          }
        }

        if (savedPath != null) {
          widget.onSaved?.call(savedPath);
          SnackbarUtil.showSuccess('Lưu chữ ký thành công!',
              alignment: "bottom");
          log('Đường dẫn: $savedPath', name: 'saveSignature');
        } else {
          throw Exception('Không thể lưu chữ ký vào thư viện');
        }
      } else {
        SnackbarUtil.showWarning('Vui lòng ký hoặc tải ảnh trước khi lưu',
            alignment: "bottom");
      }
    } catch (e) {
      log("Lỗi khi lưu chữ ký: $e");
      SnackbarUtil.showError('Lỗi khi lưu chữ ký', alignment: "bottom");
    }
  }

  void _clearSignature() {
    if (_controller.isNotEmpty) {
      _previousPoints.add(List.from(_controller.points));
    }
    _controller.clear();
    setState(() {
      _loadedImage = null; // Xóa ảnh tải lên nếu có
    });
  }

  void _undoSignature() {
    if (_previousPoints.isNotEmpty) {
      _controller.clear();
      _controller.points.addAll(_previousPoints.removeLast());
      setState(() {}); // Cập nhật giao diện
    }
  }

  Future<void> _loadImageSignature() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        setState(() {
          _loadedImage = File(image.path); // Lưu file ảnh để hiển thị
        });
        widget.onSaved?.call(image.path);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Đã tải ảnh chữ ký')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi khi tải ảnh: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: 23.w,
              height: 4.h,
              child: ButtonWidget(
                ontap: _loadImageSignature,
                leadingIcon: const Icon(
                  Icons.image,
                  size: 15,
                  color: Colors.white,
                ),
                text: 'Tải ảnh',
                backgroundColor: AppColors.primary,
                textSize: 15.sp,
                borderRadius: 5.0,
              ),
            ),
            Row(
              spacing: 5.w,
              children: [
                SizedBox(
                  width: 25.w,
                  height: 4.h,
                  child: ButtonWidget(
                    ontap: _undoSignature,
                    leadingIcon: Icon(
                      Icons.update,
                      size: 20,
                      color: Colors.amber[700] as Color,
                    ),
                    text: 'Hoàn tác',
                    textColor: Colors.amber[700] as Color,
                    textSize: 15.sp,
                    borderRadius: 5.0,
                  ),
                ),
                // ElevatedButton(
                //   onPressed: _saveSignature,
                //   child: const Text('Lưu'),
                // ),
                SizedBox(
                  width: 13.w,
                  height: 4.h,
                  child: ButtonWidget(
                    ontap: _clearSignature,
                    text: 'Xóa',
                    backgroundColor: AppColors.gray3,
                    textSize: 15.sp,
                    borderRadius: 5.0,
                  ),
                ),
              ],
            )
          ],
        ),
        SizedBox(
          height: 1.h,
        ),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            color: Colors.white,
          ),
          child: SizedBox(
            height: 200,
            child: Stack(
              children: [
                // Hiển thị ảnh tải lên nếu có
                if (_loadedImage != null)
                  Positioned.fill(
                    child: Image.file(
                      _loadedImage!,
                      fit: BoxFit.contain,
                    ),
                  ),
                // Khu vực vẽ chữ ký
                Listener(
                  onPointerUp: (_) async {
                    // Gọi hàm lưu chữ ký ngay khi bút nhấc ra
                    await _onDrawEnd();
                  },
                  child: Signature(
                    controller: _controller,
                    height: 200,
                    backgroundColor: Colors.transparent,
                  ),
                ),
              ],
            ),
          ),
        ),
        widget.errorText != null && widget.errorText!.isNotEmpty
            ? Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    widget.errorText!,
                    style: TextStyle(
                        color: Colors.red, fontSize: AppDimens.smallText),
                    textAlign: TextAlign.start,
                  )
                ],
              )
            : SizedBox(
                height: 1.h,
              ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 93.w,
              height: 4.h,
              child: ButtonWidget(
                ontap: _saveSignature,
                leadingIcon: const Icon(
                  Icons.download,
                  size: 15,
                  color: Colors.white,
                ),
                text: 'Lưu chữ ký về máy',
                backgroundColor: AppColors.gray3,
                textSize: 15.sp,
                borderRadius: 5.0,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
