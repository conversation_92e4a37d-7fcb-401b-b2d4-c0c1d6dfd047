import 'package:flutter/material.dart';

class CheckBoxWidget extends StatelessWidget {
  final String title;
  final bool value;
  final Function(bool?) onChanged;
  final Color? borderColor;
  final Color? checkedBorderColor; // <PERSON><PERSON>u viền khi checkbox được chọn
  final Color? fillColor;
  final Color? checkedFillColor;
  final Color? activeColor;
  final Color? checkColor;
  final double? size;
  final TextStyle? titleStyle;
  final EdgeInsetsGeometry? padding;
  final bool isDisabled;

  const CheckBoxWidget({
    super.key,
    required this.title,
    required this.value,
    required this.onChanged,
    this.borderColor,
    this.checkedBorderColor,
    this.fillColor,
    this.checkedFillColor,
    this.activeColor,
    this.checkColor,
    this.size = 24.0,
    this.titleStyle,
    this.padding,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.only(bottom: 20.0),
      child: Row(
        children: [
          SizedBox(
            width: size,
            height: size,
            child: Transform.scale(
              scale: size! / 24,
              child: Checkbox(
                value: value,
                onChanged: isDisabled ? null : onChanged,
                activeColor: activeColor,
                checkColor: checkColor,
                fillColor: WidgetStateProperty.resolveWith<Color?>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return checkedFillColor ?? fillColor;
                    }
                    return fillColor;
                  },
                ),
                side: WidgetStateBorderSide.resolveWith(
                  (Set<WidgetState> states) {
                    return BorderSide(
                      color: states.contains(WidgetState.selected)
                          ? checkedBorderColor ??
                              borderColor ??
                              Theme.of(context).primaryColor
                          : borderColor ??
                              Theme.of(context).unselectedWidgetColor,
                      width: 2.0,
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(width: 8.0),
          Expanded(
            child: Text(
              title,
              style: titleStyle ?? Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        ],
      ),
    );
  }
}
