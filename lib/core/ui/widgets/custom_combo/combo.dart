import 'package:attp_2024/core/configs/dimens/app_dimens.dart';
import 'package:diacritic/diacritic.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:attp_2024/core/configs/theme/app_colors.dart';
import 'package:attp_2024/core/ui/widgets/text/text_widget.dart';

// ignore: must_be_immutable
class CustomCombobox extends StatelessWidget {
  var selectedItem = ''.obs;
  var list = <DropdownModel>[].obs;
  final Function(DropdownModel? selected) onChange;
  final List<DropdownModel> dropDownList;
  final bool isEnabled;
  final bool isRequired;
  final String? errorText;
  final double? vertical;
  final double? horizontal;
  final bool? showDelete;
  final Function? delete;
  final double? borderRadius;
  final String title;
  final double? height;
  final String hintSearch;
  final String? titleSearch;
  final double? heighCombo;
  final bool showCloseButton;
  final DropdownModel? defaultSelectedItem;
  final String? Function(DropdownModel?)? validator;
  final FocusNode _focusNode = FocusNode();
  Widget? prefixIcon;
  final bool hideTitle;

  CustomCombobox({
    super.key,
    this.delete,
    this.errorText,
    this.showDelete,
    required this.dropDownList,
    required this.onChange,
    this.isEnabled = true,
    this.isRequired = false,
    this.vertical,
    this.horizontal,
    this.borderRadius,
    this.height,
    required this.title,
    this.hintSearch = "Tìm kiếm ...",
    this.titleSearch,
    this.defaultSelectedItem,
    this.heighCombo,
    this.showCloseButton = true,
    this.validator,
    this.prefixIcon,
    this.hideTitle = false,
  }) {
    selectedItem.value = defaultSelectedItem?.display ?? "";
  }
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!hideTitle)
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: AppColors.black,
                    fontSize: AppDimens.sizeTitle,
                  ),
                ),
                if (isRequired)
                  const TextSpan(
                    text: ' *',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: AppDimens.textSize14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        if (!hideTitle) const SizedBox(height: 1),
        InkWell(
          onTap: isEnabled
              ? () {
                  FocusScope.of(context).requestFocus(FocusNode());
                  _showDropdown(context);
                }
              : null, // Disable dropdown if not enabled
          child: Container(
              // width: weight,
              height: height ?? 45,
              padding: EdgeInsets.symmetric(
                horizontal: horizontal ?? 10,
                vertical: vertical ?? 12,
              ),
              decoration: BoxDecoration(
                color: isEnabled ? Colors.white : Colors.grey[200],
                border: Border.all(
                    color: errorText?.isNotEmpty ?? false
                        ? AppColors.error
                        : AppColors.borderInput1,
                    width: .3),
                borderRadius: BorderRadius.circular(borderRadius ?? 5),
              ),
              child: Obx(
                () => Row(
                  children: [
                    if (prefixIcon != null) ...[
                      prefixIcon!,
                      Gap(1.w),
                    ],
                    Padding(
                      padding: const EdgeInsets.fromLTRB(8.0, 0.0, 0.0, 0.0),
                      child: SizedBox(
                        child: TextWidget(
                          size: AppDimens.textSize14,
                          text: selectedItem.value.isNotEmpty
                              ? selectedItem.value
                              : '- Chọn -',
                          color: selectedItem.value.isNotEmpty
                              ? Colors.black // Màu đen khi có dữ liệu
                              : AppColors.borderInputDisabled,
                          maxLines: 1, // Đảm bảo văn bản không tràn ra ngoài
                        ),
                      ),
                    ),
                    const Spacer(),
                    if (selectedItem.value.isNotEmpty &&
                        (showDelete ??
                            true)) // Hiển thị nút xóa nếu có lựa chọn
                      InkWell(
                        child: Icon(
                          CupertinoIcons.clear_circled_solid,
                          color: AppColors.iconColors,
                          size: 17.sp,
                        ),
                        onTap: () {
                          if (delete != null) {
                            delete?.call();
                          }
                          selectedItem.value = ''; // Xóa lựa chọn hiện tại
                          onChange(
                              null); // Thông báo thay đổi về việc xóa dữ liệu
                        },
                      ),
                    Padding(
                      padding: const EdgeInsets.only(right: 0.0),
                      child: Icon(
                        Icons.arrow_drop_down_outlined,
                        color: AppColors.borderInputDisabled,
                        size: 20.sp,
                      ),
                    ),
                  ],
                ),
              )),
        ),
        Column(
          children: [
            SizedBox(
              height: 2.h,
              child: Text(
                errorText ?? '',
                style:
                    TextStyle(color: Colors.red, fontSize: AppDimens.smallText),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showDropdown(BuildContext context) {
    list.assignAll(dropDownList); // Ensure the full list is loaded into `list`
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: const BeveledRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(5), topRight: Radius.circular(5))),
      builder: (BuildContext context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: SingleChildScrollView(
            child: Container(
              height: heighCombo ?? 90.h,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  if (titleSearch != null)
                    Padding(
                        padding: EdgeInsets.only(
                          top: 1.h,
                          bottom: 2.5.h,
                        ),
                        child: Center(
                          child: Text(
                            titleSearch ?? "",
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        )),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 0.w),
                    child: TextField(
                      focusNode: _focusNode, // Attach focusNode
                      onChanged: (value) {
                        searchItem(value); // Search logic
                      },
                      decoration: InputDecoration(
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 8.0),
                        hintText: hintSearch,
                        hintStyle: const TextStyle(color: Colors.grey),
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(3.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[200],
                      ),
                    ),
                  ),
                  const SizedBox(height: 8.0),
                  Expanded(
                    child: Obx(() {
                      if (list.isEmpty) {
                        return Center(
                            child: Image.asset(
                                width: 30.w, 'assets/images/hover.png'));
                      }
                      return ListView.builder(
                        itemCount: list.length,
                        itemBuilder: (context, index) {
                          bool isSelected = list[index].display ==
                              selectedItem
                                  .value; // Check if the current item is selected
                          return ListTile(
                            minTileHeight: 2.h,
                            tileColor: isSelected ? Colors.blue[100] : null,
                            // Highlight selected item
                            title: Text(list[index].display ?? ''),
                            onTap: () {
                              selectedItem.value = list[index].display ?? '';
                              Navigator.pop(context);
                              onChange(list[index]); // Notify about selection
                            },
                            trailing: isSelected
                                ? const Icon(Icons.check,
                                    color: Colors
                                        .blue) // Show a check mark for the selected item
                                : null,
                          );
                        },
                      );
                    }),
                  ),
                  showCloseButton
                      ? InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                                // ignore: deprecated_member_use
                                color: Colors.red.withOpacity(.2),
                                borderRadius: BorderRadius.circular(5)),
                            margin: EdgeInsets.all(2.w),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  size: 20,
                                  Icons.close,
                                  color: Colors.red,
                                ),
                                SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  "Đóng",
                                  style: TextStyle(
                                      color: Colors.red, fontSize: 16),
                                )
                              ],
                            ),
                          ),
                        )
                      : const SizedBox()
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void searchItem(String query) {
    String normalizedQuery = removeDiacritics(query.toLowerCase());

    if (normalizedQuery.isEmpty) {
      list.assignAll(dropDownList);
    } else {
      list.assignAll(
        dropDownList.where((item) {
          String normalizedDisplay =
              removeDiacritics(item.display?.toLowerCase() ?? '');
          return normalizedDisplay.contains(normalizedQuery);
        }).toList(),
      );
    }
  }
}

class DropdownModel {
  String? display;
  String? id;

  DropdownModel({this.display, this.id});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DropdownModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
