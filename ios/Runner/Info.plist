<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Ứng dụng cần quyền nhận diện giọng nói để hỗ trợ nhập liệu bằng giọng nói.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>This app uses Face ID for secure login and authentication.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>Ứng dụng cần truy cập micro để ghi âm và hỗ trợ các tính năng hội thoại.</string>
    <key>NSMotionUsageDescription</key>
    <string>Ứng dụng sử dụng cảm biến chuyển động để cải thiện trải nghiệm thực tế tăng cường (AR).</string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>Please allow access to photo library so you can easily upload your photos.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>Please allow access to photo library so you can easily upload your photos.</string>
    <key>NSCameraUsageDescription</key>
    <string>Ứng dụng sử dụng camera để bạn có thể chụp ảnh hồ sơ và tải lên tài khoản của mình.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Ứng dụng cần truy cập mạng nội bộ để kết nối với thiết bị.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Ứng dụng cần quyền truy cập vị trí để cung cấp các tính năng liên quan đến bản đồ và dịch vụ định vị.</string>
	<key>UIFileSharingEnabled</key>
	<true/>
    <key>com.apple.security.files.user-selected.read-only</key>
    <true/>
 	<key>NSLocationWhenInUseUsageDescription</key>
    <string>Ứng dụng cần quyền truy cập vị trí để lấy tọa độ hiện tại</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>Ứng dụng cần quyền truy cập vị trí để lấy tọa độ hiện tại</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>NTSOFT ATTP</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>haugiang</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>
